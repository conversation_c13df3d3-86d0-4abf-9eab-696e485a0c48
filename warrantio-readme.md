# Warrantio - Warranty Inspection User Flow

This document outlines the user flow for submitting a new warranty inspection using the Warrantio application. The process is designed to guide the user through a series of steps, collecting necessary information and photographic evidence. The form intelligently adapts based on whether the automotive part is currently installed in a vehicle or not.

## Overview

The warranty inspection process involves:
1.  Determining if the part is installed or not.
2.  Uploading an initial image of the part's label, which undergoes OCR (Optical Character Recognition) to pre-fill details.
3.  Collecting specific information based on the installation status (vehicle details if installed, package condition if not installed).
4.  Uploading various supporting photographs, some of which may also use OCR (e.g., product label on the component, VIN label, vehicle registration).
5.  Completing a checklist relevant to the part's condition and installation.
6.  Reviewing all gathered information and submitting the inspection claim.

## Core Data Services

*   **Inspection Form (`inspection-form.tsx`):** Manages the multi-step user interface, data collection, validation, and overall flow logic.
*   **OCR Service (`ocr-service.ts`):** Processes uploaded images to automatically extract relevant text data such as part numbers, serial numbers, product codes, manufacturer details, etc., using Azure OpenAI Vision models. This service aims to reduce manual data entry and improve accuracy. If live OCR processing fails or is disabled (in development), it can provide mock data.

## Detailed User Flow

The form dynamically adjusts the steps based on user input. Progress is tracked, and users can navigate back and forth between completed steps.

### Initial Steps (Common to All Inspections)

1.  **Step 1: Installation Status**
    *   **Component:** `InstallationStatusStep`
    *   **Purpose:** Determine if the part subject to warranty is currently installed in a vehicle or not.
    *   **User Action:** Selects "Installed" or "Not Installed".
    *   **System Action:** The selection here dictates the subsequent steps in the form. If the status is changed later or reset, the form will revert to this step and clear data from irrelevant subsequent steps.

2.  **Step 2: Label Upload**
    *   **Component:** `LabelUploadStep`
    *   **Purpose:** Capture an image of the primary label on the part or its packaging.
    *   **User Action:** Uploads an image of the part's label.
    *   **System Action (OCR):**
        *   The uploaded image is sent to the `ocr-service.ts`.
        *   The OCR service attempts to extract:
            *   Part Number
            *   Product Code
            *   Serial Number
            *   Product Type
            *   Manufacturer
            *   Vehicle Compatibility
            *   Quality Grade (Premium/Standard)
            *   Expiry Date
        *   The extracted data, along with a confidence score, is used to pre-fill relevant fields in the "Part Details" section of the form.

### Scenario A: Part is "Installed"

If the user selected "Installed" in Step 1, the flow continues as follows:

3.  **Step 3A: Vehicle Information**
    *   **Component:** `VehicleInfoStep`
    *   **Purpose:** Collect details about the vehicle in which the part is (or was) installed.
    *   **User Action:** Enters information such as:
        *   Vehicle Make
        *   Vehicle Model
        *   VIN (Vehicle Identification Number)
        *   Mileage
        *   Installation Date
    *   **Photo Uploads & OCR:** This step might involve uploading images for:
        *   `VIN_LABEL`: If a photo of the VIN label is uploaded, it will be processed by the OCR service to attempt extraction of the VIN.
        *   `VEHICLE_REGISTRATION`: If a photo of the vehicle registration document is uploaded, it will be processed by OCR to extract relevant vehicle information.

4.  **Step 4A: Photo Documentation (Installed)**
    *   **Component:** `PhotoUploadStep`
    *   **Purpose:** Gather photographic evidence of the installed part, its condition, and related aspects.
    *   **User Action:** Uploads various photos based on prompts. Required photos include:
        *   `PRODUCT_OVERVIEW`: Overall condition of the part.
        *   `PRODUCT_LABEL` (on component): Photo of the label directly on the part itself. (This image *supports OCR* via `ocr-service.ts` to capture/confirm part details).
    *   Optional/Conditional photos might include:
        *   `FAULT_FOCUS`: Close-up of the defect.
        *   (VIN Label and Vehicle Registration might also be listed here if not covered in VehicleInfoStep explicitly).
    *   **System Action (OCR):** Images designated with `supportsOcr: true` (e.g., `PRODUCT_LABEL`) are processed by `ocr-service.ts`.

5.  **Step 5A: Inspection Checklist (Installed)**
    *   **Component:** `ChecklistStep`
    *   **Purpose:** Complete a checklist specific to warranty claims for installed parts.
    *   **User Action:** Answers questions like:
        *   Was a system flush performed?
        *   Was the correct coolant used?
        *   Was the thermostat replaced (if applicable)?

6.  **Step 6A: Review & Submit**
    *   **Component:** `SubmissionStep`
    *   **Purpose:** Allow the user to review all collected information and photos before final submission.
    *   **User Action:**
        *   Reviews all data entered across previous steps.
        *   Can provide additional notes.
        *   Selects a preliminary decision (e.g., Warranty, Goodwill, Deny).
        *   Submits the inspection form.
    *   **System Action:**
        *   The form data (an `InspectionFormType` object) is validated against `InspectionFormSchema`.
        *   The `createInspection` server action is called to save the inspection data.
        *   On success, the user is redirected to the details page for the newly created inspection.
        *   Toast notifications provide feedback on success or failure.

### Scenario B: Part is "Not Installed"

If the user selected "Not Installed" in Step 1, the flow continues as follows:

3.  **Step 3B: Package Condition (Uninstalled Part)**
    *   **Component:** `UninstalledPartStep`
    *   **Purpose:** Collect details about the condition of the uninstalled part and its packaging.
    *   **User Action:** Enters information such as:
        *   Box condition.
        *   Whether there is package damage (this may conditionally require a `BOX_DAMAGE` photo).
        *   If the part was stored properly.
        *   If the seal is intact.
        *   If accessories are present.
        *   Reason for the claim.

4.  **Step 4B: Photo Documentation (Uninstalled)**
    *   **Component:** `PhotoUploadStep`
    *   **Purpose:** Gather photographic evidence of the uninstalled part, its packaging, and any damage.
    *   **User Action:** Uploads various photos based on prompts. Required photos include:
        *   `FULL_BOX`: Complete packaging.
        *   `PRODUCT_OVERVIEW`: Overall condition of the part.
        *   `PRODUCT_LABEL` (on component): Photo of the label directly on the part itself. (This image *supports OCR*).
    *   Optional/Conditional photos might include:
        *   `BOX_DAMAGE`: If package damage was indicated.
        *   `FAULT_FOCUS`: Close-up of the defect.
    *   **System Action (OCR):** Images designated with `supportsOcr: true` (e.g., `PRODUCT_LABEL`) are processed by `ocr-service.ts`.

5.  **Step 5B: Inspection Checklist (Uninstalled)**
    *   **Component:** `ChecklistStep`
    *   **Purpose:** Complete a checklist specific to warranty claims for uninstalled parts.
    *   **User Action:** Answers questions relevant to uninstalled parts (the specific questions for this checklist would be defined within the `ChecklistStep` component and may differ from the "installed" checklist).

6.  **Step 6B: Review & Submit**
    *   **Component:** `SubmissionStep`
    *   **Purpose:** Allow the user to review all collected information and photos before final submission.
    *   **User Action:**
        *   Reviews all data entered across previous steps.
        *   Can provide additional notes.
        *   Selects a preliminary decision.
        *   Submits the inspection form.
    *   **System Action:** Same as Step 6A (validation, `createInspection` call, redirection, notifications).

## Photo Types and OCR Support

The `inspection-form.tsx` defines several types of photos that can be uploaded (`PHOTO_STEP_CODES`). Some of these explicitly support OCR processing:

*   **`LABEL` (Initial Label Upload):** `supportsOcr: true` - Used to pre-fill part details.
*   **`FULL_BOX`:** `supportsOcr: false`
*   **`BOX_DAMAGE`:** `supportsOcr: false` (Conditional on `hasPackageDamage`)
*   **`PRODUCT_OVERVIEW`:** `supportsOcr: false`
*   **`PRODUCT_LABEL` (On Component):** `supportsOcr: true` - Used to capture/verify details from the label on the part itself.
*   **`FAULT_FOCUS`:** `supportsOcr: false`
*   **`VIN_LABEL`:** `supportsOcr: true` - Used to capture Vehicle Identification Number.
*   **`VEHICLE_REGISTRATION`:** `supportsOcr: true` - Used to capture details from vehicle registration documents.

## Form Management

*   **State Management:** `react-hook-form` is used for managing form state, validation (with `zodResolver` and `InspectionFormSchema`), and submission.
*   **Navigation:** Users can move to the `nextStep()` or `prevStep()`. The current step and total steps are displayed along with a progress bar.
*   **Reset:** If the `installationStatus` is cleared or reset, the form navigates back to the first step, and a `resetKey` forces a re-render of the form to ensure a clean state for the new flow. Dependent data (e.g., `uninstalledInfo` if switching to "installed") is cleared to maintain data integrity.

This flow ensures that all necessary information for a warranty inspection is captured systematically, leveraging OCR to improve efficiency and accuracy where possible. 