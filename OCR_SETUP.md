# OCR Setup for Part Label Scanning

This document explains how to set up Optical Character Recognition (OCR) for the part label scanning feature in the warranty platform.

## Prerequisites

1. A Google Cloud Platform account
2. Google Cloud Vision API enabled
3. API key for Google Cloud Vision

## Setup Steps

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select an existing one
3. Make note of your project ID

### 2. Enable the Vision API

1. In the Google Cloud Console, navigate to "APIs & Services > Library"
2. Search for "Cloud Vision API"
3. Click on the API and click "Enable"

### 3. Create API Credentials

1. Go to "APIs & Services > Credentials"
2. Click "Create Credentials" and select "API key"
3. Copy your new API key
4. For better security, consider restricting this key to only the Vision API

### 4. Add Environment Variables

Add the following environment variables to your `.env.local` file:

```
# Google Cloud Vision API for OCR
GOOGLE_VISION_API_KEY="your-google-cloud-vision-api-key"

# Set to 'true' to enable mock OCR (no API calls)
NEXT_PUBLIC_USE_MOCK_OCR="false"
```

## Usage

The OCR system will now automatically scan uploaded part labels and extract the following information:

- Part number
- Product code 
- Serial number
- Product type
- Manufacturer
- Vehicle compatibility
- Quality grade (Premium 'P' or Standard 'S')
- Expiry or production date

## Fallback Mechanism

If the OCR API is unavailable or you're developing locally without an API key:

1. Set `NEXT_PUBLIC_USE_MOCK_OCR="true"` in your environment variables
2. The system will use a deterministic mock function to generate consistent demo values

## Troubleshooting

If OCR isn't working as expected:

1. Check your API key permissions in Google Cloud Console
2. Ensure the Cloud Vision API is enabled
3. Check browser console and server logs for detailed error messages
4. Verify image quality - clear, well-lit images work best
5. Test with the mock OCR mode to verify the rest of the system is working

## Customizing OCR Logic

The OCR extraction logic is in `src/lib/ocr-service.ts`. You can modify the `extractPartDataFromText` function to adapt to different label formats or extract additional information.

Key areas you may want to customize:
- Regular expressions for identifying specific data formats
- Text patterns for your specific product labels
- Logic for determining quality grade (premium vs standard) 