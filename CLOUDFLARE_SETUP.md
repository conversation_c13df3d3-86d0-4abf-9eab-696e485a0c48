# Cloudflare Images Integration Setup

This document explains how to set up Cloudflare Images integration for the image uploader component.

## Prerequisites

1. A Cloudflare account
2. Cloudflare Images subscription (can be enabled in the Cloudflare dashboard)

## Setup Steps

### 1. Get Cloudflare Credentials

You'll need the following credentials from your Cloudflare account:

- **Account ID**: Found in the dashboard URL or under Account Home
- **API Token**: Create a new API token with Images Write permissions
- **Account Hash**: Found in the Cloudflare Images documentation or in the image delivery URL

### 2. Environment Variables

Add the following environment variables to your `.env.local` file:

```
# Cloudflare Images API
CLOUDFLARE_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_HASH="your-cloudflare-account-hash-for-image-delivery"
```

Note: Make sure to use `CLOUDFLARE_API_TOKEN` (not CLOUDFLARE_IMAGES_API_TOKEN).

### 3. Usage in Components

The `ImageUploader` component is already configured to upload images to Cloudflare. It includes:

- HEIC/HEIF image conversion support
- Local preview with Cloudflare storage
- Error handling
- Progress indication
- Custom metadata support

## How It Works

1. The component converts HEIC/HEIF images to PNG if needed 
2. It creates a local preview using blob URLs
3. The image is uploaded to Cloudflare using a server action
4. The image URL and ID are returned and stored in your form data

## Troubleshooting

If you encounter issues with uploads:

1. Check your API token permissions - it needs Images Write access
2. Ensure your Cloudflare Images subscription is active
3. Verify the correct account hash is used for image delivery
4. Check browser console and server logs for detailed error messages
5. Make sure you're using the right environment variable names:
   - `CLOUDFLARE_ACCOUNT_ID` 
   - `CLOUDFLARE_API_TOKEN`
   - `NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_HASH`

## Image Delivery Options

Cloudflare Images provides multiple delivery variants. By default, we use the "public" variant, but you can create custom variants in your Cloudflare dashboard for different sizes or transformations.

The display URL format is: `https://imagedelivery.net/<account-hash>/<image-id>/public` 