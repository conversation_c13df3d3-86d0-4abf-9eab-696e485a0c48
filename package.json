{"name": "nexuskit", "version": "1.0.0", "description": "A powerful, feature-rich Next.js starter library with authentication, UI components, and database integration", "private": false, "license": "MIT", "author": "NexusKit Contributors", "repository": {"type": "git", "url": "https://github.com/nebulanollie/nexuskit.git"}, "homepage": "https://nexuskit.dev", "bugs": {"url": "https://github.com/nebulanollie/nexuskit/issues"}, "keywords": ["react", "nextjs", "typescript", "tailwindcss", "authentication", "prisma", "ui-components", "starter", "template", "boilerplate"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "prepare": "husky", "format": "prettier --write \"**/*.{js,ts,tsx,md}\"", "format:check": "prettier --check \"**/*.{js,ts,tsx,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint && npm run test:ci", "create-app": "node ./cli/create-app.js", "publish-cli": "cd cli && npm publish"}, "files": ["src", "public", "prisma", "LICENSE", "README.md"], "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@azure/cognitiveservices-computervision": "^8.2.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "0.0.34", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "culori": "^4.0.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "exceljs": "^4.4.0", "heic-convert": "^2.1.0", "heic2any": "^0.0.4", "input-otp": "^1.4.2", "jszip": "^3.10.1", "lucide-react": "^0.482.0", "migrat": "^0.2.1", "next": "15.2.3", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "openai": "^4.98.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "rimraf": "^6.0.1", "sharp": "^0.34.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/culori": "^2.1.1", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "audit-ci": "^7.0.1", "eslint": "^9", "eslint-config-next": "15.2.3", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "prisma": "^6.5.0", "react-email": "3.0.7", "tailwindcss": "^4", "typescript": "^5"}, "peerDependencies": {"next": ">=14.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "engines": {"node": ">=18.17.0"}}