datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
 
generator client {
  provider = "prisma-client-js"
}
 
model User {
  id            String          @id @default(cuid())
  name          String?
  email         String          @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]
 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
 
model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
 
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  @@id([provider, providerAccountId])
}
 
model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
 
model VerificationToken {
  identifier String
  token      String
  expires    DateTime
 
  @@id([identifier, token])
}
 
// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
 
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  @@id([userId, credentialID])
}

model Photo {
  id               String   @id @default(cuid())
  inspectionId     String
  cdnUrl           String
  cdnId            String
  caption          String
  group            String?
  ocrConfidence    Int?
  originalFileName String?
  convertedFileName String?
  createdAt        DateTime @default(now())

  inspection       Inspection @relation(fields: [inspectionId], references: [id], onDelete: Cascade)
}

model Inspection {
  id                 String   @id @default(cuid())
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  inspectorId        String?
  partType           String
  installationStatus String
  decision           String
  notes              String?

  partDetails        Json
  vehicleInfo        Json?
  uninstalledInfo    Json?
  installedChecklist Json?

  photos             Photo[]
  visits             Visit[]
}

// Model for tracking customer site visits
model Visit {
  id                String    @id @default(cuid())
  customerName      String
  companyName       String?
  location          String
  scheduledDate     DateTime
  completedDate     DateTime?
  status            String    // "scheduled", "in_progress", "completed", "canceled" 
  checksCompleted   Int       @default(0)
  totalChecks       Int       @default(0)
  notes             String?
  
  // Optional reference to an inspection
  inspectionId      String?   
  inspection        Inspection? @relation(fields: [inspectionId], references: [id], onDelete: SetNull)
  
  // Checklist items as JSON - flexible structure
  checklistItems    Json?     
  
  // Metadata
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}