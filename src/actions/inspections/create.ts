"use server";

import prisma from "@/lib/prisma";

/**
 * Persists a full inspection payload and returns the created inspection id.
 * For now the payload is loosely typed (any) – a dedicated Zod schema can be added later.
 */
export async function createInspection(payload: any): Promise<string | null> {
  try {
    const {
      photos = [],
      partDetails,
      vehicleInfo,
      uninstalledInfo,
      installedChecklist,
      language, // Extract language but don't use it (not in DB schema)
      ...rest
    } = payload;

    console.log("Creating inspection with data:", {
      ...rest,
      partDetails: { ...partDetails },
      vehicleInfo: { ...vehicleInfo },
      uninstalledInfo: { ...uninstalledInfo },
      installedChecklist: { ...installedChecklist },
      photosCount: photos.length
    });

    // @ts-expect-error prisma types generated post-migration
    const inspection = await prisma.inspection.create({
      data: {
        ...rest,
        partDetails,
        vehicleInfo,
        uninstalledInfo,
        installedChecklist,
        photos: {
          create: photos.map((p: any) => {
            let extractedCdnId;
            if (typeof p.url === 'string') {
              const parts = p.url.split('/');
              if (parts.length >= 2) { // Ensure there are enough parts to get an element at index length - 2
                extractedCdnId = parts[parts.length - 2];
              } else {
                // This case means p.url was a string but didn't have enough '/' separators
                console.error(`[createInspection] Problematic p.url for cdnId extraction (too few parts after split): '${p.url}'`);
                extractedCdnId = undefined; // This will likely cause the Prisma error
              }
            } else {
              // This case means p.url was not a string (e.g., undefined, null, or other type)
              console.error(`[createInspection] Problematic p.url for cdnId extraction (not a string or missing):`, p.url);
              extractedCdnId = undefined; // This will likely cause the Prisma error
            }

            return {
              cdnUrl: p.url, // Use p.url from the input payload
              cdnId: extractedCdnId, // Use the extracted (or undefined) cdnId
              caption: p.caption,
              group: p.group,
              ocrConfidence: p.ocrConfidence ?? null,
              originalFileName: p.originalFileName ?? null,
              convertedFileName: p.convertedFileName ?? null,
            };
          }),
        },
      },
    });

    return inspection.id;
  } catch (error) {
    console.error("Failed to create inspection", error);
    return null;
  }
} 