"use server";

import prisma from "@/lib/prisma";
import { InspectionServerSchema, type InspectionServerType } from "@/lib/inspection-schema";
import { ZodError } from "zod";

/**
 * Persists a full inspection payload and returns the created inspection id.
 * Uses server-side validation with enhanced security checks.
 */
export async function createInspection(payload: unknown): Promise<string | null> {
  try {
    // Validate payload with server-side schema
    const validatedData = InspectionServerSchema.parse(payload);

    const {
      photos = [],
      partDetails,
      vehicleInfo,
      uninstalledInfo,
      installedChecklist,
      language, // Extract language but don't use it (not in DB schema)
      ...rest
    } = validatedData;

    console.log("Creating inspection with data:", {
      ...rest,
      partDetails: { ...partDetails },
      vehicleInfo: { ...vehicleInfo },
      uninstalledInfo: { ...uninstalledInfo },
      installedChecklist: { ...installedChecklist },
      photosCount: photos.length
    });

    const inspection = await prisma.inspection.create({
      data: {
        ...rest,
        partDetails,
        vehicleInfo,
        uninstalledInfo,
        installedChecklist,
        photos: {
          create: photos.map((p: any) => {
            let extractedCdnId;
            if (typeof p.url === 'string') {
              const parts = p.url.split('/');
              if (parts.length >= 2) { // Ensure there are enough parts to get an element at index length - 2
                extractedCdnId = parts[parts.length - 2];
              } else {
                // This case means p.url was a string but didn't have enough '/' separators
                console.error(`[createInspection] Problematic p.url for cdnId extraction (too few parts after split): '${p.url}'`);
                extractedCdnId = undefined; // This will likely cause the Prisma error
              }
            } else {
              // This case means p.url was not a string (e.g., undefined, null, or other type)
              console.error(`[createInspection] Problematic p.url for cdnId extraction (not a string or missing):`, p.url);
              extractedCdnId = undefined; // This will likely cause the Prisma error
            }

            return {
              cdnUrl: p.url, // Use p.url from the input payload
              cdnId: extractedCdnId, // Use the extracted (or undefined) cdnId
              caption: p.caption,
              group: p.group,
              ocrConfidence: p.ocrConfidence ?? null,
              originalFileName: p.originalFileName ?? null,
              convertedFileName: p.convertedFileName ?? null,
            };
          }),
        },
      },
    });

    return inspection.id;
  } catch (error) {
    // Handle validation errors specifically
    if (error instanceof ZodError) {
      console.error("Validation failed:", error.errors);
      // In production, you might want to return specific validation errors
      // For now, we'll just log them and return null
      return null;
    }

    // Handle other errors
    console.error("Failed to create inspection:", error);
    return null;
  }
}