"use server";

import ExcelJ<PERSON> from "exceljs";
import prisma from "@/lib/prisma";
import { format } from "date-fns";

/**
 * Generates an Excel report for an inspection
 * @param inspectionId The ID of the inspection to export
 * @returns Base64 string representation of the Excel file and filename
 */
export async function exportInspectionToExcel(inspectionId: string): Promise<{ base64: string; filename: string } | null> {
  try {
    // @ts-expect-error - Get inspection with photos
    const inspection = await prisma.inspection.findUnique({
      where: { id: inspectionId },
      include: { photos: true },
    });

    if (!inspection) {
      console.error("Inspection not found:", inspectionId);
      return null;
    }

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "WarrantIO";
    workbook.created = new Date();

    // Add inspection details worksheet
    const detailsSheet = workbook.addWorksheet("Inspection Details");
    
    // Set up columns
    detailsSheet.columns = [
      { header: "Field", key: "field", width: 25 },
      { header: "Value", key: "value", width: 50 },
    ];

    // Add basic info
    detailsSheet.addRow({ field: "Inspection ID", value: inspection.id });
    detailsSheet.addRow({ field: "Date", value: format(new Date(inspection.createdAt), "yyyy-MM-dd HH:mm") });
    detailsSheet.addRow({ field: "Component Type", value: inspection.partType });
    detailsSheet.addRow({ field: "Status", value: inspection.installationStatus === "installed" ? "Installed" : "Not Installed" });
    detailsSheet.addRow({ field: "Decision", value: inspection.decision });

    // Part details
    detailsSheet.addRow({ field: "Part Details", value: "" });
    const partDetails = inspection.partDetails as any;
    if (partDetails) {
      detailsSheet.addRow({ field: "Part Number", value: partDetails.partNumber || "N/A" });
      detailsSheet.addRow({ field: "Product Code", value: partDetails.productCode || "N/A" });
      detailsSheet.addRow({ field: "Serial Number", value: partDetails.serialNumber || "N/A" });
      detailsSheet.addRow({ field: "Expiry Date", value: partDetails.expiryDate || "N/A" });
      detailsSheet.addRow({ field: "Quality Grade", value: partDetails.qualityGrade || "N/A" });
    }

    // Vehicle information (if available)
    if (inspection.vehicleInfo && inspection.installationStatus === "installed") {
      detailsSheet.addRow({ field: "Vehicle Information", value: "" });
      const vehicleInfo = inspection.vehicleInfo as any;
      detailsSheet.addRow({ field: "Make", value: vehicleInfo.make || "N/A" });
      detailsSheet.addRow({ field: "Model", value: vehicleInfo.model || "N/A" });
      detailsSheet.addRow({ field: "VIN", value: vehicleInfo.vinNumber || "N/A" });
      detailsSheet.addRow({ field: "Mileage", value: vehicleInfo.mileage || "N/A" });
      detailsSheet.addRow({ field: "Installation Date", value: vehicleInfo.installationDate || "N/A" });
    }

    // Uninstalled info (if available)
    if (inspection.uninstalledInfo && inspection.installationStatus === "not_installed") {
      detailsSheet.addRow({ field: "Package Condition", value: "" });
      const uninstalledInfo = inspection.uninstalledInfo as any;
      detailsSheet.addRow({ field: "Box Condition", value: uninstalledInfo.boxCondition || "N/A" });
      detailsSheet.addRow({ field: "Package Damage", value: uninstalledInfo.hasPackageDamage ? "Yes" : "No" });
      detailsSheet.addRow({ field: "Stored Properly", value: uninstalledInfo.storedProperly ? "Yes" : "No" });
      detailsSheet.addRow({ field: "Seal Intact", value: uninstalledInfo.sealIntact ? "Yes" : "No" });
      detailsSheet.addRow({ field: "Accessories Present", value: uninstalledInfo.accessoriesPresent ? "Yes" : "No" });
      detailsSheet.addRow({ field: "Reason for Claim", value: uninstalledInfo.reasonForClaim || "N/A" });
    }

    // Add notes
    detailsSheet.addRow({ field: "Notes", value: inspection.notes || "No notes provided" });

    // Photos sheet
    if (inspection.photos.length > 0) {
      const photosSheet = workbook.addWorksheet("Photos");
      
      photosSheet.columns = [
        { header: "Type", key: "type", width: 20 },
        { header: "Description", key: "description", width: 30 },
        { header: "URL", key: "url", width: 60 },
        { header: "Original File", key: "file", width: 30 },
      ];

      // Add each photo
      inspection.photos.forEach((photo: any) => {
        photosSheet.addRow({
          type: photo.group || "Other",
          description: photo.caption,
          url: photo.cdnUrl,
          file: photo.originalFileName || "N/A",
        });
      });
    }

    // Generate the Excel file
    const buffer = await workbook.xlsx.writeBuffer();
    
    // Generate filename based on part number if available
    const partNumber = (inspection.partDetails as any)?.partNumber || "unknown";
    const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
    const filename = `Inspection_${partNumber}_${timestamp}.xlsx`;
    
    // Convert buffer to base64 string
    const base64 = Buffer.from(buffer).toString('base64');

    return {
      base64,
      filename,
    };
  } catch (error) {
    console.error("Error generating Excel file:", error);
    return null;
  }
} 