"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ModeToggle } from "@/components/mode-toggle";
import { ClipboardCheck, FileText, ListChecks, LayoutDashboard, Menu, X, LogOut, User } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger, SheetClose, SheetTitle } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import Image from "next/image";

export function Header() {
  const [open, setOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const pathname = usePathname();
  
  // Close mobile menu when route changes
  useEffect(() => {
    setOpen(false);
  }, [pathname]);

  // Effect to set isClient to true after mount
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  const navItems = [
    { href: "/", label: "Dashboard", icon: <LayoutDashboard className="h-4 w-4 mr-2" /> },
    { href: "/inspection", label: "New Inspection", icon: <ClipboardCheck className="h-4 w-4 mr-2" /> },
    { href: "/inspections", label: "My Inspections", icon: <ListChecks className="h-4 w-4 mr-2" /> },
    { href: "/docs", label: "SOP Documentation", icon: <FileText className="h-4 w-4 mr-2" /> },
  ];

  const isActive = (path: string) => pathname === path;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-2">
      <div className=" flex h-20 items-center">
        <div className="flex items-center gap-2 md:gap-6">
          <Link 
            href="/" 
            className="flex items-center gap-2 font-semibold text-lg transition-colors hover:text-primary"
            aria-label="Warrantio Home"
          >
            <Image src="/logo.png" alt="Warrantio Logo" width={140} height={32} priority />
          </Link>
          
          <nav className="hidden md:flex gap-6 ml-6">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "group relative flex items-center text-sm font-medium transition-colors",
                  isActive(item.href)
                    ? "text-foreground"
                    : "text-muted-foreground hover:text-foreground"
                )}
                aria-current={isActive(item.href) ? "page" : undefined}
              >
                {item.label}
                {isActive(item.href) && (
                  <span className="absolute -bottom-[21px] left-0 right-0 h-[2px] bg-primary" />
                )}
                <span className={cn(
                  "absolute -bottom-[21px] left-0 right-0 h-[2px] bg-primary scale-x-0 transition-transform group-hover:scale-x-100",
                  isActive(item.href) ? "opacity-0" : "opacity-100"
                )} />
              </Link>
            ))}
          </nav>
        </div>
        
        <div className="flex items-center gap-3 ml-auto">
          {isClient ? (
            <>
              <ModeToggle />
              
              <div className="hidden md:flex">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-9 w-9 rounded-full" aria-label="User menu">
                      <Avatar className="h-9 w-9 border-2 border-muted transition-colors hover:border-primary">
                        <AvatarImage src="/avatar-placeholder.png" alt="User avatar" />
                        <AvatarFallback className="bg-muted text-foreground">IN</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">Inspector</p>
                        <p className="text-xs text-muted-foreground">Technical Services</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              
              <Sheet open={open} onOpenChange={setOpen}>
                <SheetTrigger asChild className="md:hidden">
                  <Button variant="outline" size="icon" aria-label="Menu">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[280px] sm:w-[350px] p-0">
                  <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                  <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between p-4 border-b">
                      <Link href="/" className="flex items-center gap-2 font-semibold">
                        <Image src="/logo.png" alt="Warrantio Logo" width={140} height={32} />
                      </Link>
                      <SheetClose asChild>
                        <Button variant="ghost" size="icon" aria-label="Close menu">
                          <X className="h-5 w-5" />
                        </Button>
                      </SheetClose>
                    </div>
                    
                    <nav className="flex flex-col p-4 overflow-y-auto">
                      {navItems.map((item) => (
                        <SheetClose asChild key={item.href}>
                          <Link
                            href={item.href}
                            className={cn(
                              "flex items-center text-sm font-medium py-3 px-4 rounded-md transition-colors",
                              isActive(item.href)
                                ? "bg-primary/10 text-primary"
                                : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                            )}
                            onClick={() => setOpen(false)}
                            aria-current={isActive(item.href) ? "page" : undefined}
                          >
                            {item.icon}
                            {item.label}
                          </Link>
                        </SheetClose>
                      ))}
                    </nav>
                    
                    <div className="mt-auto p-4 border-t">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10 border-2 border-muted">
                            <AvatarImage src="/avatar-placeholder.png" alt="User avatar" />
                            <AvatarFallback className="bg-muted text-foreground">IN</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">Inspector</div>
                            <div className="text-muted-foreground text-xs">Technical Services</div>
                          </div>
                        </div>
                        
                        <Button variant="destructive" size="sm" className="gap-1">
                          <LogOut className="h-4 w-4" />
                          Sign Out
                        </Button>
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </>
          ) : (
            null 
          )}
        </div>
      </div>
    </header>
  );
} 