"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { InstallationStatusStep } from "./steps/installation-status-step";
import { VehicleInfoStep } from "./steps/vehicle-info-step";
import { UninstalledPartStep } from "./steps/uninstalled-part-step";
import { PhotoUploadStep } from "./steps/photo-upload-step";
import { ChecklistStep } from "./steps/checklist-step";
import { SubmissionStep } from "./steps/submission-step";
import { LabelUploadStep } from "./steps/label-upload-step";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { createInspection } from "@/actions/inspections/create";
import { useRouter } from "next/navigation";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { InspectionFormSchema, InspectionFormType } from "@/lib/inspection-schema";
import { useToast } from "@/components/ui/use-toast";

// Define the part types according to SOP
const PART_TYPES = [
  { id: "radiator", label: "Radiator" },
  { id: "condenser", label: "Condenser" },
  { id: "viscofan", label: "Visco Fan" },
  { id: "thermostat", label: "Thermostat" },
  { id: "expansiontank", label: "Expansion Tank" },
  { id: "other", label: "Other" },
];

// Define photo requirements by step code
export const PHOTO_STEP_CODES = {
  LABEL: { 
    id: "label", 
    label: "Main Label (Uploaded)",
    description: "The main product or box label captured during the dedicated label upload step.", 
    installedRequired: false,
    uninstalledRequired: false,
    supportsOcr: true
  },
  FULL_BOX: { 
    id: "full_box", 
    label: "Full Box/Packaging", 
    description: "Photo of the complete packaging",
    installedRequired: false,
    uninstalledRequired: true,
    supportsOcr: false
  },
  BOX_DAMAGE: { 
    id: "box_damage", 
    label: "Box Damage", 
    description: "Photo of any damage to the packaging",
    installedRequired: false,
    uninstalledRequired: false,
    conditionalOn: "uninstalledInfo.hasPackageDamage",
    supportsOcr: false
  },
  PRODUCT_OVERVIEW: { 
    id: "product_overview", 
    label: "Product Overview", 
    description: "Photo showing the overall condition of the part",
    installedRequired: true,
    uninstalledRequired: true,
    supportsOcr: false
  },
  PRODUCT_LABEL: { 
    id: "product_label", 
    label: "On-Component Label (Uninstalled Part)",
    description: "If the part is uninstalled and out of its box, a photo of any label directly on the component.",
    installedRequired: false,
    uninstalledRequired: true,
    supportsOcr: true
  },
  FAULT_FOCUS: { 
    id: "fault_focus", 
    label: "Fault Focus", 
    description: "Close-up photo of the defect (leaks, bent fins, etc.)",
    installedRequired: false,
    uninstalledRequired: false,
    supportsOcr: false
  },
  VIN_LABEL: { 
    id: "vin_label", 
    label: "VIN Label", 
    description: "Photo of the vehicle identification number",
    installedRequired: false,
    uninstalledRequired: false,
    supportsOcr: true
  },
  VEHICLE_REGISTRATION: {
    id: "vehicle_registration",
    label: "Vehicle Registration (Ruhsat)",
    description: "Photo of vehicle registration document",
    installedRequired: true,
    uninstalledRequired: false,
    supportsOcr: true
  }
};

// Decision options
export const DECISION_OPTIONS = [
  { id: "warranty", label: "Warranty" },
  { id: "goodwill", label: "Goodwill" },
  { id: "deny", label: "Deny" }
];

// Define the form data interface
interface InspectionFormData {
  partType: string;
  installationStatus: string; // "installed" or "not_installed"
  partDetails: {
    partNumber: string;
    productCode: string;
    serialNumber: string;
    expiryDate: string;
    manufacturer: string;
    qualityGrade: string;
    vehicleCompatibility: string;
  };
  vehicleInfo: {
    make: string;
    model: string;
    vinNumber: string;
    mileage: string;
    installationDate: string;
  };
  uninstalledInfo: {
    boxCondition: string;
    hasPackageDamage: boolean;
    storedProperly: boolean;
    sealIntact: boolean;
    accessoriesPresent: boolean;
    reasonForClaim: string;
  };
  installedChecklist: {
    flushPerformed: boolean;
    correctCoolant: boolean;
    thermostatReplaced: boolean;
  };
  photos: { 
    id: string; 
    url: string; 
    caption: string; 
    ocrConfidence?: number;
    group?: string;
    originalFileName?: string;
    convertedFileName?: string;
  }[];
  decision: string;
  notes: string;
}

export default function WarrantyInspectionForm() {
  const { toast, error: showError } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [resetKey, setResetKey] = useState(0);
  const methods = useForm<InspectionFormType>({
    resolver: zodResolver(InspectionFormSchema),
    defaultValues: {
      language: "en",
      partType: "",
      installationStatus: undefined,
      partDetails: {
        partNumber: "",
        productCode: "",
        serialNumber: "",
        expiryDate: "",
        manufacturer: "",
        qualityGrade: "standard",
        vehicleCompatibility: "",
      },
      vehicleInfo: {
        make: "",
        model: "",
        vinNumber: "",
        mileage: "",
        installationDate: "",
      },
      uninstalledInfo: {
        boxCondition: "",
        hasPackageDamage: false,
        storedProperly: false,
        sealIntact: false,
        accessoriesPresent: false,
        reasonForClaim: "",
      },
      installedChecklist: {
        flushPerformed: false,
        correctCoolant: false,
        thermostatReplaced: false,
      },
      photos: [],
      decision: "",
      notes: "",
    },
    mode: "onBlur",
  });
  const { watch, setValue, getValues, trigger, formState: { errors } } = methods;
  const formData = watch();

  const router = useRouter();

  // Watch for changes to installation status and update steps accordingly
  useEffect(() => {
    console.log("Form data changed, installation status:", formData.installationStatus);
    
    // If installation status is reset, go back to step 0
    if (formData.installationStatus === undefined && currentStep > 0) {
      console.log("Installation status reset detected, moving to step 0");
      setCurrentStep(0);
    }
  }, [formData.installationStatus, currentStep]);
  
  // Define the step labels and components based on the installation status
  const getSteps = () => {
    console.log("Getting steps, current installation status:", formData.installationStatus);
    
    const commonSteps = [
      { label: "Installation Status", component: InstallationStatusStep },
      { label: "Label Upload", component: LabelUploadStep },
    ];

    const installedSteps = [
      { label: "Vehicle Information", component: VehicleInfoStep },
      { label: "Photo Documentation", component: PhotoUploadStep },
      { label: "Inspection Checklist", component: ChecklistStep },
      { label: "Review & Submit", component: SubmissionStep },
    ];

    const uninstalledSteps = [
      { label: "Package Condition", component: UninstalledPartStep },
      { label: "Photo Documentation", component: PhotoUploadStep },
      { label: "Inspection Checklist", component: ChecklistStep },
      { label: "Review & Submit", component: SubmissionStep },
    ];

    // If installation status is not set, or is explicitly set to undefined or null
    if (!formData.installationStatus) {
      console.log("No installation status, returning common steps");
      return commonSteps;
    }

    // Validate that installationStatus is one of the expected values
    if (formData.installationStatus !== "installed" && formData.installationStatus !== "not_installed") {
      console.warn("Invalid installation status:", formData.installationStatus);
      return commonSteps;
    }

    const allSteps = [
      ...commonSteps,
      ...(formData.installationStatus === "installed" ? installedSteps : uninstalledSteps),
    ];
    
    console.log("Returning all steps:", allSteps.map(s => s.label).join(", "));
    return allSteps;
  };

  // Get the current steps based on installation status
  const steps = getSteps();

  // Calculate progress percentage
  const progress = ((currentStep + 1) / steps.length) * 100;

  // Handler for updating form data
  const updateFormData = (fieldPath: string, value: any) => {
    setValue(fieldPath as any, value);
  };

  // Navigation handlers
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  // Direct reset function for installation status
  const resetInstallationStatus = () => {
    console.log("Direct reset of installation status");
    
    // Create a new form state with installation status undefined
    const newState = {
      ...methods.getValues(),
      installationStatus: undefined
    };
    
    // Reset with the new state
    methods.reset(newState);
    
    // Reset step to first screen
    setCurrentStep(0);
    
    // Increment reset key to force re-render
    setResetKey(prev => prev + 1);
    
    console.log("Form reset complete, new state:", newState);
  };

  // If the user selects installation status, update step order
  const handleInstallationStatusChange = (status: string | undefined | null) => {
    console.log("Installation status changing to:", status);
    
    // If status is null or undefined, reset the form
    if (status === null || status === undefined) {
      resetInstallationStatus();
      return;
    }

    console.log("Setting installation status to:", status);
    updateFormData("installationStatus", status);
    
    // Reset any data that might be inconsistent with new status
    if (status === "installed") {
      updateFormData("uninstalledInfo", {
        boxCondition: "",
        hasPackageDamage: false,
        storedProperly: false,
        sealIntact: false,
        accessoriesPresent: false,
        reasonForClaim: "",
      });
    } else {
      updateFormData("vehicleInfo", {
        make: "",
        model: "",
        vinNumber: "",
        mileage: "",
        installationDate: "",
      });
      updateFormData("installedChecklist", {
        flushPerformed: false,
        correctCoolant: false,
        thermostatReplaced: false,
      });
    }
  };

  // Generate excel handler

  // Submit handler
  const handleSubmit = methods.handleSubmit(async (data) => {
    try {
      const id = await createInspection(data);
      if (id) {
        router.push(`/inspections/${id}`);
      } else {
        showError("Failed to save inspection. Please try again.");
      }
    } catch (err) {
      console.error(err);
      showError("An unexpected error occurred while saving the inspection.");
    }
  });

  // Render the current step component1
  const StepComponent = steps[currentStep].component;

  return (
    <FormProvider {...methods}>
      <form className="space-y-4 sm:space-y-6" onSubmit={handleSubmit} key={resetKey}>
        <Progress value={progress} className="h-2" />
        
        <div className="flex flex-col items-center sm:flex-row sm:justify-between gap-1 text-xs sm:text-sm text-muted-foreground px-2 sm:px-0">
          <span>Step {currentStep + 1} of {steps.length}</span>
          <span className="text-center sm:text-right font-medium">{steps[currentStep].label}</span>
        </div>

        <Tabs value={currentStep.toString()} className="mt-2 sm:mt-4">
          {steps.map((step, index) => (
            <TabsContent key={index} value={index.toString()} className="mt-0 pt-2 sm:pt-4">
              <Card className="p-2 sm:p-4 md:p-6">
                <StepComponent 
                  formData={formData}
                  updateFormData={updateFormData}
                  partTypes={PART_TYPES}
                  photoStepCodes={PHOTO_STEP_CODES}
                  decisionOptions={DECISION_OPTIONS}
                  onInstallationStatusChange={handleInstallationStatusChange}
                  errors={errors}
                />
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        <div className="flex flex-col sm:flex-row justify-between gap-4 mt-8">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
            className="w-full sm:w-auto"
          >
            <ChevronLeft className="mr-2 h-4 w-4" /> Previous
          </Button>
          
          {currentStep === steps.length - 1 ? (
            <Button type="submit" className="w-full sm:w-auto">
              Submit Inspection
            </Button>
          ) : (
            <Button type="button" onClick={nextStep} className="w-full sm:w-auto">
              Next <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </FormProvider>
  );
} 