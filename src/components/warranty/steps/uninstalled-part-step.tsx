"use client";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Package, HelpCircle } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { InspectionFormDataType } from "@/types/inspection";

interface UninstalledPartStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
}

export function UninstalledPartStep({ formData, updateFormData }: UninstalledPartStepProps) {
  const boxConditions = [
    { id: "excellent", label: "Excellent", description: "Box is in perfect condition" },
    { id: "good", label: "Good", description: "Minor wear, no structural damage" },
    { id: "fair", label: "Fair", description: "Visible wear, minor damage" },
    { id: "poor", label: "Poor", description: "Significant damage or deterioration" },
  ];

  // Ensure uninstalledInfo exists
  const uninstalledInfo = formData.uninstalledInfo || {
    boxCondition: "",
    hasPackageDamage: false,
    storedProperly: false,
    sealIntact: false,
    accessoriesPresent: false,
    reasonForClaim: ""
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <Package className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Package Condition</h3>
          <p className="text-sm text-muted-foreground">
            Assess the condition of the packaging for the uninstalled part
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="boxCondition" className="text-base">Box Condition</Label>
          <RadioGroup
            value={uninstalledInfo.boxCondition || ""}
            onValueChange={(value) => updateFormData("uninstalledInfo.boxCondition", value)}
            className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2"
          >
            {boxConditions.map((condition) => (
              <div
                key={condition.id}
                className={`border rounded-md px-3 py-2 cursor-pointer transition-all ${
                  uninstalledInfo.boxCondition === condition.id
                    ? "border-primary bg-primary/5"
                    : "hover:border-primary/50"
                }`}
                onClick={() => updateFormData("uninstalledInfo.boxCondition", condition.id)}
              >
                <div className="flex items-center gap-2">
                  <RadioGroupItem
                    value={condition.id}
                    id={condition.id}
                    className="mt-0"
                  />
                  <Label htmlFor={condition.id} className="cursor-pointer font-medium">
                    {condition.label}
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  {condition.description}
                </p>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-x-2">
            <Label htmlFor="hasPackageDamage" className="text-base">
              Package Damage?
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Indicate if the packaging shows any signs of damage</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="hasPackageDamage"
            checked={uninstalledInfo.hasPackageDamage || false}
            onCheckedChange={(checked) => 
              updateFormData("uninstalledInfo.hasPackageDamage", checked)
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-x-2">
            <Label htmlFor="sealIntact" className="text-base">Packaging Seal Intact?</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Check if original seals are unbroken</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="sealIntact"
            checked={uninstalledInfo.sealIntact || false}
            onCheckedChange={(checked) => 
              updateFormData("uninstalledInfo.sealIntact", checked)
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-x-2">
            <Label htmlFor="accessoriesPresent" className="text-base">All Accessories Present?</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Verify that all included accessories and parts are present</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="accessoriesPresent"
            checked={uninstalledInfo.accessoriesPresent || false}
            onCheckedChange={(checked) => 
              updateFormData("uninstalledInfo.accessoriesPresent", checked)
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-x-2">
            <Label htmlFor="storedProperly" className="text-base">Properly Stored?</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Was the part stored in appropriate conditions (dry, clean, etc.)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="storedProperly"
            checked={uninstalledInfo.storedProperly || false}
            onCheckedChange={(checked) => 
              updateFormData("uninstalledInfo.storedProperly", checked)
            }
          />
        </div>
      </div>

      <Separator className="my-4" />

      <div className="space-y-2">
        <Label htmlFor="reasonForClaim" className="text-base">Reason for Warranty Claim</Label>
        <Textarea
          id="reasonForClaim"
          placeholder="Explain the reason for submitting a warranty claim..."
          value={uninstalledInfo.reasonForClaim || ""}
          onChange={(e) => updateFormData("uninstalledInfo.reasonForClaim", e.target.value)}
          rows={4}
        />
      </div>
      
      <div className="bg-muted/30 p-4 rounded-md text-sm text-muted-foreground mt-4">
        <p>
          <strong>SOP Note:</strong> For uninstalled parts, you will need to document:
        </p>
        <ul className="list-disc ml-5 mt-2 space-y-1">
          <li>Photos of packaging (all sides if damaged)</li>
          <li>Close-up photos of any visible damage</li>
          <li>Photo of label with part and serial numbers</li>
          <li>Photo of the part itself if accessible without damaging sealed packaging</li>
        </ul>
      </div>
    </div>
  );
} 