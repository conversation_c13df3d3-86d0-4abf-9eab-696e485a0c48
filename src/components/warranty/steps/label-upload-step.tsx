"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, FileImage, Tag, RefreshCw, CheckCircle, X } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ImageUploader } from "@/components/ui/image-uploader";
import { parseProductCode } from "@/lib/product-code";
import { useFormContext } from "react-hook-form";
import { InspectionFormDataType } from "@/types/inspection";
import { useToast } from "@/components/ui/use-toast";

interface LabelUploadStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
  partTypes: { id: string; label: string }[];
}

export function LabelUploadStep({ formData, updateFormData, partTypes }: LabelUploadStepProps) {
  const [activeTab, setActiveTab] = useState<string>("upload");
  const [ocrInProgress, setOcrInProgress] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [ocrResults, setOcrResults] = useState<any | null>(null);
  const [earlyProcessingDone, setEarlyProcessingDone] = useState(false);
  const [localFileData, setLocalFileData] = useState<{ file: File, previewUrl: string } | null>(null);
  const { setValue } = useFormContext();
  const { success, error: showError } = useToast();

  // Is this an installed or uninstalled part?
  const isInstalled = formData.installationStatus === "installed";
  
  // Add fallbacks for possibly undefined objects
  const partDetails = formData.partDetails || {
    partNumber: "",
    productCode: "",
    serialNumber: "",
    expiryDate: "",
    manufacturer: "",
    qualityGrade: "standard",
    vehicleCompatibility: ""
  };
  
  const photos = formData.photos || [];
  
  // Find if there's already a label photo uploaded
  const existingLabelPhoto = photos.find(photo => photo.id === "label");

  // Preload OCR module for faster processing
  useEffect(() => {
    // Preload OCR service to avoid delay on first use
    const preloadOcr = async () => {
      try {
        await import("@/lib/ocr-service");
        console.log("OCR service preloaded");
      } catch (err) {
        console.error("Failed to preload OCR service", err);
      }
    };
    
    preloadOcr();
  }, []);

  // Handle file selection (before upload completes)
  const handleFileSelected = async (fileData: { file: File, previewUrl: string, originalFileName: string }) => {
    // Store file data for potential use later
    setLocalFileData({ file: fileData.file, previewUrl: fileData.previewUrl });
    
    // Utility to convert File to Base64 Data URL
    const fileToDataURL = (file: File): Promise<string> => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
    };

    // Start OCR processing immediately with the local file
    try {
      setOcrInProgress(true);
      setErrorMessage(null);
      
      if (!ocrResults) {
        success("Starting early image processing...");
      }
      
      const imageBase64 = await fileToDataURL(fileData.file); // Convert file to base64

      const { processImageOCR } = await import("@/lib/ocr-service");
      
      let result = await processImageOCR(imageBase64, "partLabel");
      if (!result) {
        console.warn("Early OCR processing failed. User will be notified if post-upload OCR also fails.");
        // Do not set error message here for early processing, 
        // as the main OCR after upload will still attempt.
      }
      
      // Store results and update UI only if successful
      if (result) {
        setOcrResults(result);
        setEarlyProcessingDone(true);
        applyOcrResults(result);
      } else {
        // If early real OCR failed, we can note it but rely on main processOcr error handling.
        console.log("Early OCR attempt failed, will rely on main OCR process.");
      }
      
    } catch (error) {
      console.error("Error in early OCR processing:", error);
      // Don't show error to user here, we'll fall back to normal processing
    } 
  };

  // Handle image upload completion
  const handleImageUploaded = (imageData: {
    id: string;
    url: string;
    caption: string;
    originalFileName: string;
    convertedFileName?: string;
    imageId: string;
  }) => {
    // Create a descriptive caption based on installation status
    const caption = isInstalled ? "Product Label (on part)" : "Box Label";
    
    // Create the photo object for the form data
    const newPhoto = {
      id: "label",
      url: imageData.url,
      caption,
      originalFileName: imageData.originalFileName,
      convertedFileName: imageData.convertedFileName,
      imageId: imageData.imageId
    };
    
    // Update photos in formData
    const updatedPhotos = photos.filter(photo => photo.id !== "label");
    updatedPhotos.push(newPhoto);
    updateFormData("photos", updatedPhotos);
    
    // If early processing already completed, add confidence to the photo and skip OCR
    if (earlyProcessingDone && ocrResults) {
      // Update the photo with OCR confidence
      const updatedPhotosWithConfidence = updatedPhotos.map(photo => {
        if (photo.id === "label") {
          return {
            ...photo,
            ocrConfidence: ocrResults.confidence
          };
        }
        return photo;
      });
      updateFormData("photos", updatedPhotosWithConfidence);
      
      // Set the active tab to details
      setOcrInProgress(false);
      setActiveTab("details");
      return;
    }
    
    // Auto-start OCR processing if early processing wasn't done
    processOcr(imageData.url);
  };
  
  // Apply OCR results to form
  const applyOcrResults = (result: any) => {
    if (!result || !('partNumber' in result)) return;
    
    // Update form data with OCR results
    updateFormData("partDetails.partNumber", result.partNumber || "");
    updateFormData("partDetails.productCode", result.productCode || "");
    updateFormData("partDetails.serialNumber", result.serialNumber || "");
    
    // Parse component type from part number
    const parsedCode = parseProductCode(result.partNumber || "");
    if (parsedCode && parsedCode.componentName) {
      // Set component type based on parsed code
      updateFormData("partType", parsedCode.componentName);
      if (parsedCode.quality) {
        updateFormData("partDetails.qualityGrade", parsedCode.quality);
      }
    }
    
    // If we have an expiry date in YYYYMMDD format, convert it to MM/YYYY
    if (result.expiryDate && result.expiryDate.length === 8) {
      const year = result.expiryDate.substring(0, 4);
      const month = result.expiryDate.substring(4, 6);
      updateFormData("partDetails.expiryDate", `${month}/${year}`);
    }
    
    // Add quality grade if detected
    if (result.qualityGrade) {
      updateFormData("partDetails.qualityGrade", result.qualityGrade);
    }
    
    // Add manufacturer if detected
    if (result.manufacturer) {
      updateFormData("partDetails.manufacturer", result.manufacturer);
    }
    
    // Add vehicle compatibility if detected
    if (result.vehicleCompatibility) {
      updateFormData("partDetails.vehicleCompatibility", result.vehicleCompatibility);
    }
    
    // Add OCR confidence to the photo
    const updatedPhotos = photos.map(photo => {
      if (photo.id === "label") {
        return {
          ...photo,
          ocrConfidence: result.confidence
        };
      }
      return photo;
    });
    updateFormData("photos", updatedPhotos);
    
    // Show success toast with confidence level
    const confidenceLevel = result.confidence >= 80 ? "high" : "moderate";
    success(`Label information extracted with ${confidenceLevel} confidence`);
  };
  
  // Function to process OCR
  const processOcr = async (imageUrl: string) => {
    setOcrInProgress(true);
    setErrorMessage(null);
    setOcrResults(null);
    
    try {
      // Import our OCR processing functions
      const { processImageOCR } = await import("@/lib/ocr-service");
      
      // Process the image with OCR
      console.log("Using real OCR processing with Azure OpenAI Vision API");
      let result = await processImageOCR(imageUrl, "partLabel");
        
      if (!result) {
        console.warn("OCR processing failed. Details will need to be entered manually.");
        setErrorMessage("Automated information extraction failed. Please review and enter details manually.");
        showError("OCR Error: Could not read label details.");
        // No longer falling back to mock: result remains null or undefined
      }
      
      console.log("OCR result:", result);
      
      // Store the results for confirmation, even if null from a failed real OCR
      setOcrResults(result);
      
      // Auto-apply results only if successful
      if (result) {
        applyOcrResults(result);
      } // If result is null due to OCR failure, manual entry is expected
      
    } catch (error) {
      console.error("Error during OCR processing:", error);
      setErrorMessage("Failed to extract information from the image. You can still enter details manually.");
      showError("OCR processing failed. Please enter part details manually.");
    } finally {
      setOcrInProgress(false);
      setActiveTab("details");
    }
  };
  
  // Function to manually trigger OCR
  const handleRetryOcr = () => {
    // If we have local file data, use that for faster processing
    if (localFileData && localFileData.previewUrl) {
      processOcr(localFileData.previewUrl);
      return;
    }
    
    if (existingLabelPhoto?.url) {
      processOcr(existingLabelPhoto.url);
    }
  };
  
  // Handle upload error
  const handleUploadError = (error: string) => {
    setErrorMessage(error);
    showError(error);
  };

  // Remove the uploaded label so users can try again
  const handleRemoveLabel = () => {
    // Remove the label photo from form data
    const updatedPhotos = photos.filter(photo => photo.id !== 'label');
    updateFormData('photos', updatedPhotos);
    // Reset local states
    setLocalFileData(null);
    setOcrResults(null);
    setErrorMessage(null);
    setEarlyProcessingDone(false);
    // Return to upload tab
    setActiveTab('upload');
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <Tag className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">
            {isInstalled ? "Product Label" : "Box Label"}
          </h3>
          <p className="text-sm text-muted-foreground">
            {isInstalled 
              ? "Upload a clear photo of the label on the product itself" 
              : "Upload a clear photo of the label on the product packaging"}
          </p>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
        <TabsList className="grid w-full grid-cols-1 sm:grid-cols-2">
          <TabsTrigger value="upload">Upload Label</TabsTrigger>
          <TabsTrigger value="details">Part Details</TabsTrigger>
        </TabsList>
        
        <TabsContent value="upload" className="pt-4">
          <Card>
            <CardContent className="pt-6">
              {errorMessage && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errorMessage}</AlertDescription>
                </Alert>
              )}
              
              <ImageUploader
                id="label"
                label={isInstalled ? "Upload Product Label Image" : "Upload Box Label Image"}
                description={isInstalled 
                  ? "The label should be visible on the physical part" 
                  : "The label should be clearly visible on the box packaging"}
                buttonText="Select Label Image"
                placeholderText={`Upload a clear image of the ${isInstalled ? "product" : "box"} label`}
                existingImage={existingLabelPhoto}
                onImageUploaded={handleImageUploaded}
                onError={handleUploadError}
                onRetry={handleRetryOcr}
                onFileSelected={handleFileSelected}
                onRemove={handleRemoveLabel}
              />
              
              {ocrInProgress && (
                <div className="flex items-center justify-center p-4 mt-4 text-center">
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  <span>{earlyProcessingDone ? "Processing complete, finalizing..." : "Reading information from label..."}</span>
                </div>
              )}
              
              {earlyProcessingDone && !ocrInProgress && (
                <div className="mt-4 p-3 border border-green-200 bg-green-50 rounded-md">
                  <div className="flex items-center text-green-700 gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">Early processing complete!</span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">Details are already extracted while the upload finishes.</p>
                </div>
              )}
              
              {ocrResults && !ocrInProgress && (
                <div className="mt-4 border rounded-md p-3 bg-muted/10">
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="font-medium text-sm">Detected Information</h4>
                    <Badge 
                      variant={ocrResults.confidence >= 80 ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {ocrResults.confidence}% confidence
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-2 gap-y-1 text-xs mt-2">
                    <div className="text-muted-foreground">Part Number:</div>
                    <div className="font-medium">{ocrResults.partNumber || "—"}</div>
                    
                    <div className="text-muted-foreground">Serial Number:</div>
                    <div className="font-medium">{ocrResults.serialNumber || "—"}</div>
                    
                    <div className="text-muted-foreground">Manufacturer:</div>
                    <div className="font-medium">{ocrResults.manufacturer || "—"}</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="details" className="pt-4">
          <Card>
            <CardContent className="pt-6 space-y-4">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="partNumber">Part Number</Label>
                <Input 
                  id="partNumber" 
                  value={partDetails.partNumber} 
                  onChange={(e) => {
                    const value = e.target.value;
                    updateFormData("partDetails.partNumber", value);
                    
                    // Parse component type from part number
                    const parsedCode = parseProductCode(value);
                    if (parsedCode && parsedCode.componentName) {
                      // Set component type based on parsed code
                      updateFormData("partType", parsedCode.componentName);
                      if (parsedCode.quality) {
                        updateFormData("partDetails.qualityGrade", parsedCode.quality);
                      }
                    }
                  }}
                  placeholder="Enter part number"
                />
                <p className="text-xs text-muted-foreground">As shown on the label (e.g., AC 662 0008)</p>
              </div>
              
              {/* Display detected component type and quality if available */}
              {formData.partType && (
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm">Component:</span>
                  <Badge variant="outline" className="text-xs">
                    {formData.partType}
                  </Badge>
                  {partDetails.qualityGrade && (
                    <Badge 
                      variant={partDetails.qualityGrade === "premium" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {partDetails.qualityGrade === "premium" ? "Premium" : "Standard"}
                    </Badge>
                  )}
                </div>
              )}
              
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="productCode">Product Code</Label>
                <Input 
                  id="productCode" 
                  value={partDetails.productCode} 
                  onChange={(e) => updateFormData("partDetails.productCode", e.target.value)}
                  placeholder="Enter product code"
                />
                <p className="text-xs text-muted-foreground">Secondary identification code if present</p>
              </div>
              
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="serialNumber">Serial Number</Label>
                <Input 
                  id="serialNumber" 
                  value={partDetails.serialNumber} 
                  onChange={(e) => updateFormData("partDetails.serialNumber", e.target.value)}
                  placeholder="Enter serial number"
                />
                <p className="text-xs text-muted-foreground">Unique serial number of the part</p>
              </div>
              
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="manufacturer">Manufacturer</Label>
                <Input 
                  id="manufacturer" 
                  value={partDetails.manufacturer} 
                  onChange={(e) => updateFormData("partDetails.manufacturer", e.target.value)}
                  placeholder="Enter manufacturer name"
                />
                <p className="text-xs text-muted-foreground">Manufacturer of the part (e.g., MAHLE BEHR)</p>
              </div>
              
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="vehicleCompatibility">Vehicle Compatibility</Label>
                <Input 
                  id="vehicleCompatibility" 
                  value={partDetails.vehicleCompatibility} 
                  onChange={(e) => updateFormData("partDetails.vehicleCompatibility", e.target.value)}
                  placeholder="Enter compatible vehicles"
                />
                <p className="text-xs text-muted-foreground">Compatible vehicle models (e.g., BUICK REGAL, OPEL VECTRA B)</p>
              </div>
              
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="qualityGrade">Quality Grade</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="qualityGradeStandard"
                      name="qualityGrade"
                      value="standard"
                      checked={partDetails.qualityGrade === "standard"}
                      onChange={() => updateFormData("partDetails.qualityGrade", "standard")}
                      className="h-4 w-4 text-primary"
                    />
                    <label htmlFor="qualityGradeStandard">Standard (S)</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="qualityGradePremium"
                      name="qualityGrade"
                      value="premium"
                      checked={partDetails.qualityGrade === "premium"}
                      onChange={() => updateFormData("partDetails.qualityGrade", "premium")}
                      className="h-4 w-4 text-primary"
                    />
                    <label htmlFor="qualityGradePremium">Premium (P)</label>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">Quality grade of the part</p>
              </div>
              
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="expiryDate">Production/Expiry Date</Label>
                <Input 
                  id="expiryDate" 
                  value={partDetails.expiryDate} 
                  onChange={(e) => updateFormData("partDetails.expiryDate", e.target.value)}
                  placeholder="MM/YYYY"
                />
                <p className="text-xs text-muted-foreground">Production date or expiration date if applicable</p>
              </div>
              
              {ocrInProgress && (
                <div className="flex items-center justify-center p-4 text-center">
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  <span>Reading information from label...</span>
                </div>
              )}
              
              {existingLabelPhoto && !ocrInProgress && (
                <div className="flex justify-center mt-4">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleRetryOcr}
                    className="flex items-center"
                  >
                    <RefreshCw className="mr-2 h-3 w-3" />
                    Retry OCR processing
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 