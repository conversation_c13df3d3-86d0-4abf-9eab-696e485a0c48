"use client";

import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ClipboardList, HelpCircle } from "lucide-react";
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { InspectionFormDataType } from "@/types/inspection";

interface ChecklistStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
  decisionOptions: { id: string; label: string }[];
}

export function ChecklistStep({ formData, updateFormData, decisionOptions }: ChecklistStepProps) {
  // Determine which checklist to show based on installation status
  const isInstalled = formData.installationStatus === "installed";
  
  // Ensure the objects exist to prevent errors
  const installedChecklist = formData.installedChecklist || {
    flushPerformed: false,
    correctCoolant: false,
    thermostatReplaced: false
  };
  
  const uninstalledInfo = formData.uninstalledInfo || {
    sealIntact: false,
    accessoriesPresent: false,
    storedProperly: false,
    reasonForClaim: ""
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <ClipboardList className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Inspection Checklist</h3>
          <p className="text-sm text-muted-foreground">
            Complete the required checks for this {isInstalled ? "installed" : "uninstalled"} part
          </p>
        </div>
      </div>

      <Separator />

      {isInstalled ? (
        // Installed part checklist
        <div className="space-y-4 sm:space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Label htmlFor="flushPerformed" className="text-base">Flush performed?</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Verify if the cooling system was properly flushed before installing the new part</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="flushPerformed"
                checked={installedChecklist.flushPerformed || false}
                onCheckedChange={(checked) => 
                  updateFormData("installedChecklist.flushPerformed", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Label htmlFor="correctCoolant" className="text-base">Correct coolant used?</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Check if the manufacturer-recommended coolant type was used</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="correctCoolant"
                checked={installedChecklist.correctCoolant || false}
                onCheckedChange={(checked) => 
                  updateFormData("installedChecklist.correctCoolant", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Label htmlFor="thermostatReplaced" className="text-base">Thermostat replaced?</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Confirm if the thermostat was replaced during the installation</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="thermostatReplaced"
                checked={installedChecklist.thermostatReplaced || false}
                onCheckedChange={(checked) => 
                  updateFormData("installedChecklist.thermostatReplaced", checked)
                }
              />
            </div>
          </div>
        </div>
      ) : (
        // Uninstalled part checklist
        <div className="space-y-4 sm:space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Label htmlFor="sealIntact" className="text-base">Packaging seal intact?</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Check if the original packaging seals are unbroken</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="sealIntact"
                checked={uninstalledInfo.sealIntact || false}
                onCheckedChange={(checked) => 
                  updateFormData("uninstalledInfo.sealIntact", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Label htmlFor="accessoriesPresent" className="text-base">Accessories present?</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Verify that all included accessories and parts are present</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="accessoriesPresent"
                checked={uninstalledInfo.accessoriesPresent || false}
                onCheckedChange={(checked) => 
                  updateFormData("uninstalledInfo.accessoriesPresent", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Label htmlFor="storedProperly" className="text-base">Storage conditions acceptable?</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground inline" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Assess if the part was stored in appropriate conditions (not exposed to elements, etc.)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="storedProperly"
                checked={uninstalledInfo.storedProperly || false}
                onCheckedChange={(checked) => 
                  updateFormData("uninstalledInfo.storedProperly", checked)
                }
              />
            </div>

            <div className="space-y-2 mt-4">
              <Label htmlFor="reasonForClaim">Reason for Claim</Label>
              <Textarea
                id="reasonForClaim"
                placeholder="Enter the reason for the warranty claim..."
                value={uninstalledInfo.reasonForClaim || ""}
                onChange={(e) => updateFormData("uninstalledInfo.reasonForClaim", e.target.value)}
              />
            </div>
          </div>
        </div>
      )}

      <Separator className="my-6" />

      {/* Preliminary Decision - Common to both flows */}
      <div className="space-y-4">
        <Label htmlFor="decision" className="text-base font-medium">Preliminary Decision</Label>
        <Select
          value={formData.decision || ""}
          onValueChange={(value) => updateFormData("decision", value)}
        >
          <SelectTrigger id="decision" className="w-full">
            <SelectValue placeholder="Select a preliminary decision" />
          </SelectTrigger>
          <SelectContent>
            {decisionOptions.map((option) => (
              <SelectItem key={option.id} value={option.id}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-sm text-muted-foreground mt-2">
          This is your preliminary assessment. Final warranty determination will be made by the Warranty Team.
        </p>
      </div>
    </div>
  );
} 