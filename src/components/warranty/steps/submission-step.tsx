"use client";

import { Separator } from "@/components/ui/separator";
import { <PERSON>lipboardCheck, CheckCircle, AlertCircle, Car, Package, FileSpreadsheet, Download, RefreshCw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Check } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useFormContext } from "react-hook-form";
import { InspectionFormDataType } from "@/types/inspection";
import { useToast } from "@/components/ui/use-toast";
// @ts-ignore: no types available for jszip
import JSZip from "jszip";

interface SubmissionStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
  partTypes: { id: string; label: string }[];
  decisionOptions: { id: string; label: string }[];
  errors: any;
  onInstallationStatusChange: (status: string | undefined | null) => void;
}

export function SubmissionStep({ 
  formData, 
  updateFormData, 
  partTypes, 
  decisionOptions, 
  errors,
  onInstallationStatusChange 
}: SubmissionStepProps) {
  const { formState } = useFormContext();
  const [additionalNotes, setAdditionalNotes] = useState(formData.notes || "");
  const [generatingExcel, setGeneratingExcel] = useState(false);
  const [downloadingPhotos, setDownloadingPhotos] = useState(false);
  const { success } = useToast();
  
  const { register } = useFormContext();
  const hasErrors = errors && Object.keys(errors).length > 0;
  
  // Add fallbacks
  const photos = formData.photos || [];
  const partDetails = formData.partDetails || { partNumber: "", serialNumber: "" };
  const installationStatus = formData.installationStatus || "";
  
  // Find the part type label
  const partTypeLabel = partTypes.find(p => p.id === formData.partType)?.label || "Unknown part";
  const decisionLabel = decisionOptions.find(d => d.id === formData.decision)?.label || "Not selected";
  
  // Generate inspection ID
  const inspectionId = `INS-${partDetails.partNumber?.substring(0, 6) || "UNKNOWN"}-${Date.now().toString().substring(6)}`;
  
  // Check if all required fields are filled
  const isInstalled = installationStatus === "installed";
  
  const isVehicleInfoComplete = isInstalled ? 
    Boolean(formData.vehicleInfo?.make && formData.vehicleInfo?.model && formData.vehicleInfo?.mileage) : true;
  
  const isUninstalledInfoComplete = !isInstalled ?
    Boolean(formData.uninstalledInfo?.boxCondition && formData.uninstalledInfo?.reasonForClaim) : true;
  
  const isPartDetailsComplete = Boolean(partDetails.partNumber && partDetails.serialNumber);
  
  const arePhotosComplete = photos.length >= 2; // At least 2 photos
  
  const isDecisionSelected = Boolean(formData.decision);
  
  const isSubmissionReady = partTypeLabel && 
    installationStatus && 
    isVehicleInfoComplete && 
    isUninstalledInfoComplete && 
    isPartDetailsComplete && 
    arePhotosComplete &&
    isDecisionSelected;
  
  // Handle notes update
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setAdditionalNotes(e.target.value);
    updateFormData("notes", e.target.value);
  };
  
  // Handle Excel export
  const handleExcelExport = () => {
    setGeneratingExcel(true);
    
    // Simulate Excel generation
    setTimeout(() => {
      const fileName = installationStatus === "installed" 
        ? `INS_${partDetails.partNumber || "UNKNOWN"}_${new Date().toISOString().split('T')[0]}.xlsx`
        : `NI_${partDetails.partNumber || "UNKNOWN"}_${new Date().toISOString().split('T')[0]}.xlsx`;
      
      console.log(`Excel generated: ${fileName}`);
      success(`Excel file "${fileName}" generated and will be downloaded.`);
      setGeneratingExcel(false);
    }, 1500);
  };

  const handleDownloadPhotos = async () => {
    if (photos.length === 0) return;
    setDownloadingPhotos(true);
    try {
      const zip = new JSZip();
      await Promise.all(photos.map(async (photo, idx) => {
        const response = await fetch(photo.url);
        const blob = await response.blob();
        const extension = photo.url.split('.').pop()?.split(/#|\?/)[0] || 'jpg';
        const filename = `${photo.id || `photo_${idx}`}.${extension}`;
        zip.file(filename, blob);
      }));
      const content = await zip.generateAsync({ type: 'blob' });
      const zipName = `${inspectionId}_photos.zip`;
      const blobUrl = URL.createObjectURL(content);
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = zipName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(blobUrl);
      success(`Downloaded photos as ${zipName}`);
    } catch (err) {
      console.error("Error downloading photos:", err);
      success("Failed to download photos.");
    } finally {
      setDownloadingPhotos(false);
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <ClipboardCheck className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Review & Submit</h3>
          <p className="text-sm text-muted-foreground">
            Final review of inspection details and warranty decision
          </p>
        </div>
      </div>

      {hasErrors && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please fix the following errors before submitting:
            <ul className="list-disc pl-5 mt-2">
              {errors.partDetails?.partNumber && (
                <li>Part number is required</li>
              )}
              {errors.installationStatus && (
                <li>Installation status is required</li>
              )}
              {errors.decision && (
                <li>Decision is required</li>
              )}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <Separator />
      
      <div className="bg-muted/20 p-4 rounded-lg border">
        <div className="flex justify-between items-center mb-2">
          <h4 className="font-medium text-base">Inspection Summary</h4>
          <Badge variant="outline">{inspectionId}</Badge>
        </div>
        <div className="text-sm text-muted-foreground mb-3">
          {new Date().toLocaleDateString()} • {installationStatus === "installed" ? "Installed Part" : "Uninstalled Part"}
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-3 text-sm">
          <div>
            <div className="text-muted-foreground">Part Type</div>
            <div className="font-medium">{partTypeLabel}</div>
          </div>
          
          <div>
            <div className="text-muted-foreground">Part Number</div>
            <div className="font-medium">{partDetails.partNumber || "—"}</div>
          </div>
          
          <div>
            <div className="text-muted-foreground">Serial Number</div>
            <div className="font-medium">{partDetails.serialNumber || "—"}</div>
          </div>
          
          <div>
            <div className="text-muted-foreground">Decision</div>
            <div>
              {formData.decision ? (
                <Badge 
                  variant={
                    formData.decision === "warranty" ? "default" : 
                    formData.decision === "goodwill" ? "outline" : 
                    "destructive"
                  }
                >
                  {decisionLabel}
                </Badge>
              ) : "—"}
            </div>
          </div>
          
          <div>
            <div className="text-muted-foreground">Photo Count</div>
            <div className="font-medium">{photos.length} photos</div>
          </div>
        </div>
      </div>
      
      {/* Detailed Sections */}
      <div className="space-y-6 mt-2">
        {/* Part Information */}
        <div className="bg-card rounded-lg border p-4">
          <h4 className="font-medium mb-3 flex items-center justify-between">
            <span>Part Information</span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onInstallationStatusChange(undefined)}
              className="text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Change Installation Status
            </Button>
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">            
            <div className="text-muted-foreground">Installation Status</div>
            <div className="font-medium flex items-center">
              {installationStatus === "installed" ? (
                <>
                  <Car className="h-4 w-4 mr-1 text-primary" /> Installed in vehicle
                </>
              ) : (
                <>
                  <Package className="h-4 w-4 mr-1 text-primary" /> Not Installed
                </>
              )}
            </div>
            
            <div className="text-muted-foreground">Language</div>
            <div className="font-medium">
              {formData.language === "en" ? "English" : formData.language === "tr" ? "Türkçe" : formData.language}
            </div>
          </div>
        </div>
        
        {/* Vehicle Information (if installed) */}
        {installationStatus === "installed" && formData.vehicleInfo && (
          <div className="bg-card rounded-lg border p-4">
            <h4 className="font-medium mb-3">Vehicle Information</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
              <div className="text-muted-foreground">Make</div>
              <div className="font-medium">{formData.vehicleInfo.make || "—"}</div>
              
              <div className="text-muted-foreground">Model</div>
              <div className="font-medium">{formData.vehicleInfo.model || "—"}</div>
              
              <div className="text-muted-foreground">VIN</div>
              <div className="font-medium">{formData.vehicleInfo.vinNumber || "—"}</div>
              
              <div className="text-muted-foreground">Mileage</div>
              <div className="font-medium">{formData.vehicleInfo.mileage || "—"}</div>
              
              <div className="text-muted-foreground">Installation Date</div>
              <div className="font-medium">{formData.vehicleInfo.installationDate || "—"}</div>
            </div>
            
            {/* Installed Checklist */}
            {formData.installedChecklist && (
              <div className="mt-4 border-t pt-3">
                <h5 className="font-medium mb-2 text-sm">Maintenance Checklist</h5>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="text-muted-foreground">Flush Performed:</div>
                    <div>
                      {formData.installedChecklist.flushPerformed ? (
                        <Badge variant="default" className="font-normal">Yes</Badge>
                      ) : (
                        <Badge variant="outline" className="font-normal">No</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="text-muted-foreground">Correct Coolant:</div>
                    <div>
                      {formData.installedChecklist.correctCoolant ? (
                        <Badge variant="default" className="font-normal">Yes</Badge>
                      ) : (
                        <Badge variant="outline" className="font-normal">No</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="text-muted-foreground">Thermostat Replaced:</div>
                    <div>
                      {formData.installedChecklist.thermostatReplaced ? (
                        <Badge variant="default" className="font-normal">Yes</Badge>
                      ) : (
                        <Badge variant="outline" className="font-normal">No</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Uninstalled Information (if uninstalled) */}
        {installationStatus === "not_installed" && formData.uninstalledInfo && (
          <div className="bg-card rounded-lg border p-4">
            <h4 className="font-medium mb-3">Package Information</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
              <div className="text-muted-foreground">Box Condition</div>
              <div className="font-medium">
                {formData.uninstalledInfo.boxCondition ? (
                  <Badge variant={
                    formData.uninstalledInfo.boxCondition === "good" ? "default" :
                    formData.uninstalledInfo.boxCondition === "fair" ? "outline" : "destructive"
                  }>
                    {formData.uninstalledInfo.boxCondition.charAt(0).toUpperCase() + formData.uninstalledInfo.boxCondition.slice(1)}
                  </Badge>
                ) : "—"}
              </div>
              
              <div className="text-muted-foreground">Package Damaged</div>
              <div>
                {formData.uninstalledInfo.hasPackageDamage ? (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" /> Yes
                  </Badge>
                ) : (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" /> No
                  </Badge>
                )}
              </div>
              
              <div className="text-muted-foreground">Storage Acceptable</div>
              <div>
                {formData.uninstalledInfo.storedProperly ? (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" /> Yes
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" /> No
                  </Badge>
                )}
              </div>
              
              <div className="text-muted-foreground">Seal Intact</div>
              <div>
                {formData.uninstalledInfo.sealIntact ? (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" /> Yes
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" /> No
                  </Badge>
                )}
              </div>
              
              <div className="text-muted-foreground">Accessories Present</div>
              <div>
                {formData.uninstalledInfo.accessoriesPresent ? (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" /> Yes
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" /> No
                  </Badge>
                )}
              </div>
            </div>
            
            {formData.uninstalledInfo.reasonForClaim && (
              <div className="mt-3 text-sm">
                <div className="text-muted-foreground mb-1">Reason for Claim</div>
                <div className="bg-muted/20 p-2 rounded-sm">{formData.uninstalledInfo.reasonForClaim}</div>
              </div>
            )}
          </div>
        )}
        
        {/* Photos Summary */}
        <div className="bg-card rounded-lg border p-4">
          <h4 className="font-medium mb-3">Photo Documentation</h4>
          {photos.length > 0 ? (
            <>
              <p className="text-sm sm:hidden">{photos.length} photos uploaded.</p>
              <div className="hidden sm:grid grid-cols-1 md:grid-cols-2 gap-2">
                {photos.map((photo) => (
                  <div key={photo.id} className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    <span>{photo.caption}</span>
                    {photo.ocrConfidence && (
                      <Badge variant={photo.ocrConfidence >= 80 ? "outline" : "secondary"} className="text-xs">
                        OCR {photo.ocrConfidence}%
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </>
          ) : (
            <p className="text-sm text-muted-foreground">No photos uploaded yet.</p>
          )}
        </div>
        
        {/* Additional Notes */}
        <div className="space-y-2">
          <Label htmlFor="final-notes">Additional Notes</Label>
          <Textarea 
            id="final-notes" 
            placeholder="Additional inspection notes..."
            className="min-h-[100px]"
            {...register("notes")}
          />
        </div>
        
        {/* Excel Export */}
        <div className="bg-primary/5 rounded-lg border p-4 mt-6">
          <div className="flex items-center gap-3 mb-3">
            <FileSpreadsheet className="h-5 w-5 text-primary" />
            <h4 className="font-medium">Excel Report Generation</h4>
          </div>
          
          <p className="text-sm mb-4">
            Generate an Excel workbook with inspection details that will be saved to your device and synced to the cloud when online.
          </p>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:gap-4">
            <Button
              className="w-full sm:w-auto"
              variant="outline"
              onClick={handleExcelExport}
              disabled={!isSubmissionReady || generatingExcel}
            >
              {generatingExcel ? (
                "Generating Excel..."
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" /> Export Excel Workbook
                </>
              )}
            </Button>
            <Button
              className="w-full sm:w-auto mt-2 sm:mt-0"
              variant="outline"
              onClick={handleDownloadPhotos}
              disabled={photos.length === 0 || downloadingPhotos}
            >
              {downloadingPhotos ? (
                "Downloading Photos..."
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" /> Download Photos
                </>
              )}
            </Button>
          </div>
        </div>
        
        {/* Validation Status */}
        {!isSubmissionReady && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Incomplete Information</AlertTitle>
            <AlertDescription>
              <p className="mb-2">Please complete all required fields before submitting:</p>
              <ul className="list-disc list-inside text-sm">
                {!formData.partType && <li>Select a part type</li>}
                {!formData.installationStatus && <li>Specify installation status</li>}
                {!isVehicleInfoComplete && <li>Complete vehicle information</li>}
                {!isUninstalledInfoComplete && <li>Complete package condition information</li>}
                {!isPartDetailsComplete && <li>Missing part details (upload LABEL photo for OCR)</li>}
                {!arePhotosComplete && <li>Upload required photos</li>}
                {!isDecisionSelected && <li>Select a preliminary decision</li>}
              </ul>
            </AlertDescription>
          </Alert>
        )}
        
        {isSubmissionReady && (
          <Alert className="border-green-500 text-green-500 bg-green-500/10">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Ready to Submit</AlertTitle>
            <AlertDescription>
              All required information has been provided. You can now export to Excel and submit the inspection.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
} 