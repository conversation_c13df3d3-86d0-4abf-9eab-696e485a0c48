"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Car, HelpCircle, CalendarIcon, Upload, FileImage, RefreshCw, CheckCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useState, useEffect } from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { ImageUploader } from "@/components/ui/image-uploader";
import { InspectionFormDataType } from "@/types/inspection";

interface VehicleInfoStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
}

export function VehicleInfoStep({ formData, updateFormData }: VehicleInfoStepProps) {
  const [activeTab, setActiveTab] = useState<string>("upload");
  const [ocrInProgress, setOcrInProgress] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Set fallbacks for possibly undefined objects
  const vehicleInfo = formData.vehicleInfo || {
    make: "",
    model: "",
    vinNumber: "",
    mileage: "",
    installationDate: ""
  };
  
  // Find if there's already a vehicle registration photo uploaded
  const existingRegistrationPhoto = formData.photos?.find(photo => photo.id === "vehicle_registration");

  // Handle image upload completion
  const handleImageUploaded = (imageData: {
    id: string;
    url: string;
    caption: string;
    originalFileName: string;
    convertedFileName?: string;
    imageId: string;
  }) => {
    console.log("Vehicle Registration: Image uploaded successfully", imageData);
    
    // Create the photo object for the form data
    const newPhoto = {
      id: "vehicle_registration",
      url: imageData.url,
      caption: "Vehicle Registration Document (Ruhsat)",
      originalFileName: imageData.originalFileName,
      convertedFileName: imageData.convertedFileName,
      imageId: imageData.imageId
    };
    
    // Update photos in formData
    const updatedPhotos = formData.photos?.filter(photo => photo.id !== "vehicle_registration") || [];
    updatedPhotos.push(newPhoto);
    updateFormData("photos", updatedPhotos);
    
    // Auto-start OCR processing
    processOcr(imageData.url);
  };
  
  // Function to process OCR
  const processOcr = async (imageUrl: string) => {
    console.log("Vehicle Registration: Starting OCR processing for:", imageUrl);
    setOcrInProgress(true);
    setErrorMessage(null);
    
    try {
      // Check if we should use mock processing (for development without API key)
      const useMockOcr = process.env.NEXT_PUBLIC_USE_MOCK_OCR === "true";
      
      // Import our OCR processing functions
      const { processImageOCR, mockOCRProcessing } = await import("@/lib/ocr-service");
      
      // Define ExtractedPartData based on expected structure, or import if already defined
      // This is a placeholder, adjust if you have a specific type
      type ExtractedPartData = {
        vehicleCompatibility?: string;
        serialNumber?: string;
        confidence?: number;
        [key: string]: any; // Allow other properties
      };

      // Process the image with OCR
      let ocrResult: ExtractedPartData | null | undefined;

      if (useMockOcr) {
        console.log("Using mock OCR processing");
        ocrResult = await mockOCRProcessing(imageUrl, "vehicleRegistration"); 
      } else {
        console.log("Using real OCR processing with Google Cloud Vision API");
        ocrResult = await processImageOCR(imageUrl, "vehicleRegistration"); 
        
        if (!ocrResult) {
          console.warn("OCR processing failed. Details will need to be entered manually.");
          setErrorMessage("Automated information extraction failed from vehicle registration. Please review and enter details manually.");
          // We need a toast here as well, assuming useToast is available or can be added.
          // showError("OCR Error: Could not read vehicle registration details."); // Assuming showError is available via useToast
        }
      }
      
      // Extract vehicle information from OCR text if OCR was successful
      let make = "";
      let model = "";
      let vin = "";
      // let mileage = ""; // Mileage is less likely to be on a registration card consistently
      // let firstRegistrationDate = ""; // Also less consistent or might need specific parsing
      // let documentDate = "";

      if (ocrResult) {
        console.log("Vehicle OCR Result:", ocrResult); // Log the actual result
        // Standardized fields from OcrResult for 'vehicleRegistration'
        make = ocrResult.make || "";
        model = ocrResult.model || "";
        vin = ocrResult.vin || "";
        // mileage = ocrResult.mileage || ""; // If your OCR service returns these
        // firstRegistrationDate = ocrResult.firstRegistrationDate || "";
        // documentDate = ocrResult.documentDate || "";

        // Add OCR confidence to the photo
        const updatedPhotos = formData.photos?.map(photo => {
          if (photo.id === "vehicle_registration") {
            return {
              ...photo,
              ocrConfidence: ocrResult.confidence 
            };
          }
          return photo;
        }) || [];
        updateFormData("photos", updatedPhotos);
      } else {
        console.log("OCR Result was null, skipping data application for vehicle info.");
      }
      
      // Update form data with OCR results (or empty strings if failed)
      updateFormData("vehicleInfo.make", make);
      updateFormData("vehicleInfo.model", model);
      updateFormData("vehicleInfo.vinNumber", vin);
      
      console.log("Vehicle Registration: OCR processing complete");
      setActiveTab("details");
      
    } catch (error) {
      console.error("Error during OCR processing:", error);
      setErrorMessage("An error occurred while trying to read the vehicle registration. Please enter details manually.");
      // showError("OCR Processing Error. Please enter vehicle details manually."); // Assuming showError from useToast
    } finally {
      setOcrInProgress(false);
    }
  };
  
  // Function to manually trigger OCR
  const handleRetryOcr = () => {
    console.log("Vehicle Registration: Retry OCR button clicked");
    if (existingRegistrationPhoto?.url) {
      processOcr(existingRegistrationPhoto.url);
    }
  };
  
  // Handle error from uploader
  const handleUploadError = (error: string) => {
    console.error("Vehicle Registration: Upload error:", error);
    setErrorMessage(error);
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <Car className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Vehicle Information</h3>
          <p className="text-sm text-muted-foreground">
            Enter details about the vehicle where the part was installed
          </p>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
        <TabsList className="grid w-full grid-cols-1 sm:grid-cols-2">
          <TabsTrigger value="upload">Upload Ruhsat</TabsTrigger>
          <TabsTrigger value="details">Vehicle Details</TabsTrigger>
        </TabsList>
        
        <TabsContent value="upload" className="pt-4">
          <Card>
            <CardContent className="pt-6">
              {errorMessage && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errorMessage}</AlertDescription>
                </Alert>
              )}
              
              <ImageUploader
                id="vehicle_registration"
                label="Upload Vehicle Registration (Ruhsat)"
                description="The document should show vehicle make, model, and VIN"
                buttonText="Select Document"
                placeholderText="Upload a clear image of the vehicle registration document"
                existingImage={existingRegistrationPhoto}
                onImageUploaded={handleImageUploaded}
                onError={handleUploadError}
                onRetry={handleRetryOcr}
              />
              
              {ocrInProgress && (
                <div className="flex items-center justify-center p-4 mt-4 text-center">
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  <span>Reading information from registration document...</span>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="details" className="pt-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="make">Make</Label>
                <Input
                  id="make"
                  placeholder="Vehicle manufacturer"
                  value={vehicleInfo.make}
                  onChange={(e) => updateFormData("vehicleInfo.make", e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  placeholder="Vehicle model"
                  value={vehicleInfo.model}
                  onChange={(e) => updateFormData("vehicleInfo.model", e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vinNumber">VIN Number</Label>
              <Input
                id="vinNumber"
                placeholder="Vehicle identification number"
                value={vehicleInfo.vinNumber}
                onChange={(e) => updateFormData("vehicleInfo.vinNumber", e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="mileage">Mileage (km)</Label>
                <Input
                  id="mileage"
                  type="number"
                  placeholder="Current mileage"
                  value={vehicleInfo.mileage}
                  onChange={(e) => updateFormData("vehicleInfo.mileage", e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="installationDate">Installation Date</Label>
                <Input
                  id="installationDate"
                  type="date"
                  value={vehicleInfo.installationDate}
                  onChange={(e) => updateFormData("vehicleInfo.installationDate", e.target.value)}
                />
              </div>
            </div>

            {ocrInProgress && (
              <div className="flex items-center justify-center p-4 text-center">
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                <span>Reading information from registration document...</span>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 