"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Camera, Upload, X, ImageIcon } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ImageUploader } from "@/components/ui/image-uploader";
import { InspectionFormDataType } from "@/types/inspection";

interface PhotoUploadStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
  photoStepCodes: {
    [key: string]: PhotoCategoryType;
  };
}

// Define a type for individual photo categories for clarity
type PhotoCategoryType = {
  id: string;
  label: string;
  description: string;
  installedRequired: boolean;
  uninstalledRequired: boolean;
  conditionalOn?: string;
  supportsOcr: boolean;
};

export function PhotoUploadStep({ formData, updateFormData, photoStepCodes }: PhotoUploadStepProps) {
  const [activeTab, setActiveTab] = useState<string>("");
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  // Add fallbacks
  const photos = formData.photos || [];
  const uninstalledInfo = formData.uninstalledInfo || { hasPackageDamage: false };
  const installationStatus = formData.installationStatus || "not_installed";

  const [blobUrls, setBlobUrls] = useState<string[]>([]);

  // Cleanup blob URLs when component unmounts
  useEffect(() => {
    return () => {
      blobUrls.forEach(url => URL.revokeObjectURL(url));
    };
  }, [blobUrls]);

  // Get the appropriate photo types based on installation status
  const getPhotoTypes = (): PhotoCategoryType[] => {
    const isInstalled = installationStatus === "installed";

    return Object.values(photoStepCodes).filter(photoType => {
      // Always include label for review
      if (photoType.id === "label") {
        return true;
      }

      // Include registration only for installed parts
      if (photoType.id === "vehicle_registration") {
        return isInstalled;
      }

      // Base requirement flag based on status
      const statusMatch = isInstalled
        ? photoType.installedRequired
        : photoType.uninstalledRequired;

      // Handle conditional requirements (e.g., box damage)
      if (photoType.conditionalOn) {
        const pathParts = photoType.conditionalOn.split('.');
        if (pathParts.length === 1) {
          return statusMatch && formData[pathParts[0]] === true;
        } else if (pathParts.length === 2 && formData[pathParts[0]]) {
          return statusMatch && formData[pathParts[0]][pathParts[1]] === true;
        }
        return false;
      }

      return statusMatch;
    });
  };

  // Get photo categories for tabs
  const photoCategories = getPhotoTypes();
  
  // Set the first tab as active if none is selected
  useEffect(() => {
    if (photoCategories.length > 0 && !activeTab && photoCategories[0]) {
      setActiveTab(photoCategories[0].id);
    }
  }, [photoCategories, activeTab]);
  
  // Handle image upload completion
  const handleImageUploaded = (imageData: {
    id: string;
    url: string;
    caption: string;
    originalFileName: string;
    convertedFileName?: string;
    imageId: string;
  }) => {
    if (!activeTab) return;
    
    const photoType = photoStepCodes[activeTab.toUpperCase()] || 
                     { label: "Uploaded Photo", supportsOcr: false };
    
    // Generate a unique ID (stable, collision-safe)
    const photoId = `${activeTab}_${window.crypto.randomUUID()}`;
    
    // Create the new photo object
    const newPhoto = {
      id: photoId,
      url: imageData.url,
      caption: `${photoType.label}`,
      group: activeTab,
      originalFileName: imageData.originalFileName,
      convertedFileName: imageData.convertedFileName,
      imageId: imageData.imageId,
      // Demo OCR confidence between 60-100% for supported photo types
      ...(photoType.supportsOcr && {
        ocrConfidence: Math.floor(Math.random() * 41) + 60
      })
    };
    
    // Add to photos array
    updateFormData("photos", [...photos, newPhoto]);
  };
  
  // Remove a photo
  const removePhoto = (photoId: string) => {
    // Filter out the photo with the given ID
    const updatedPhotos = photos.filter(photo => photo.id !== photoId);
    updateFormData("photos", updatedPhotos);
  };
  
  // Get photos for the current tab
  const getPhotosForCategory = (categoryId: string) => {
    if (!categoryId) return [];
    return photos.filter(photo => 
      photo.group === categoryId || photo.id.startsWith(categoryId)
    );
  };

  // Handle error from uploader
  const handleUploadError = (errorMessage: string) => {
    setUploadError(errorMessage);
  };

  // Determine current step index for stepper view
  const currentStepIndex = photoCategories.findIndex(c => c.id === activeTab);
  const stepProgress = photoCategories.length > 0 ? ((currentStepIndex + 1) / photoCategories.length) * 100 : 0;

  const activeCategory = photoCategories.find(cat => cat.id === activeTab);

  // Helper function to render content for a category
  const renderCategoryContent = (category: PhotoCategoryType | undefined) => {
    if (!category) return null;
    const photosForThisCategory = getPhotosForCategory(category.id);

    return (
      <div className="space-y-4 mt-4">
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full">
            <Camera className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h4 className="font-medium">{category.label}</h4>
            <p className="text-sm text-muted-foreground mt-1">
              {category.description}
            </p>
          </div>
        </div>
        
        <ImageUploader
          id={category.id}
          label={category.label}
          description={category.description}
          buttonText="Upload Photo"
          placeholderText="Click to select or drag an image here"
          onImageUploaded={handleImageUploaded}
          onError={handleUploadError}
          allowMultiple={["product_overview", "fault_focus"].includes(category.id)}
          showPreview={false}
        />
        
        {uploadError && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{uploadError}</AlertDescription>
          </Alert>
        )}
        
        {photosForThisCategory.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">Saved Photos</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {photosForThisCategory.map((photo) => (
                <div key={photo.id} className="relative group">
                  <div className="aspect-square bg-muted/20 rounded-md overflow-hidden border relative">
                    <img 
                      src={photo.url} 
                      alt={photo.caption} 
                      className="object-cover w-full h-full"
                    />
                    {photo.convertedFileName && (
                      <Badge className="absolute bottom-1 left-1 bg-primary/90 text-xs">
                        Converted
                      </Badge>
                    )}
                    <Button
                      variant="destructive"
                      size="icon"
                      className="h-6 w-6 absolute top-1 right-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity"
                      onClick={() => removePhoto(photo.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="text-xs mt-1 truncate hidden sm:block">
                    {photo.caption}
                  </div>
                  {photo.ocrConfidence && (
                    <Badge 
                      variant={photo.ocrConfidence > 80 ? "outline" : "secondary"} 
                      className="mt-1 text-xs hidden sm:inline-flex"
                    >
                      OCR: {photo.ocrConfidence}%
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <Camera className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Photo Documentation</h3>
          <p className="text-sm text-muted-foreground">
            Upload the required photos for warranty inspection
          </p>
        </div>
      </div>
      
      <Separator />
      
      {/* Mobile Stepper and Content Area */}
      <div className="sm:hidden space-y-3">
        {photoCategories.length > 0 && activeCategory && (
          <>
            <div className="text-sm text-muted-foreground flex justify-between">
              <span>Step {currentStepIndex + 1} of {photoCategories.length}</span>
              <span>{activeCategory.label}</span>
            </div>
            <div className="w-full bg-muted/30 h-2 rounded-full overflow-hidden">
              <div className="h-full bg-primary" style={{ width: `${stepProgress}%` }} />
            </div>
            <div className="flex justify-between gap-2">
              <Button
                variant="outline"
                size="sm"
                type="button"
                className="flex-1"
                disabled={currentStepIndex === 0}
                onClick={() => {
                  const prev = photoCategories[currentStepIndex - 1];
                  if (prev) setActiveTab(prev.id);
                }}
              >
                Prev
              </Button>
              <Button
                size="sm"
                type="button"
                className="flex-1"
                disabled={currentStepIndex === photoCategories.length - 1}
                onClick={() => {
                  const next = photoCategories[currentStepIndex + 1];
                  if (next) setActiveTab(next.id);
                }}
              >
                Next
              </Button>
            </div>
            {/* Render active category content for mobile */}
            {renderCategoryContent(activeCategory)}
          </>
        )}
         {photoCategories.length === 0 && (
          <p className="text-sm text-muted-foreground text-center py-4">No photo types applicable for the current selection.</p>
        )}
      </div>

      {/* Tabs for larger screens */}
      <Tabs value={activeTab || ""} onValueChange={setActiveTab} className="w-full hidden sm:block">
        <TabsList className="w-full overflow-x-auto flex flex-nowrap justify-start">
          {photoCategories.map((category) => (
            <TabsTrigger 
              key={category.id} 
              value={category.id}
              className="flex-shrink-0"
            >
              {category.label}
              {getPhotosForCategory(category.id).length > 0 && category.id === activeTab && (
                <Badge className="ml-2 bg-primary" variant="default">
                  {getPhotosForCategory(category.id).length}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>
        
        {photoCategories.map((category) => (
          <TabsContent key={category.id} value={category.id} className="mt-0">
            {/* Render category content for desktop tabs */}
            {renderCategoryContent(category)}
          </TabsContent>
        ))}
         {photoCategories.length === 0 && (
          <TabsContent value="no-photos">
             <p className="text-sm text-muted-foreground text-center py-4">No photo types applicable for the current selection.</p>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
} 