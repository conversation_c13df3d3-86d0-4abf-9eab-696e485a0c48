"use client";

import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Activity, RefreshCw, RotateCcw, CheckCircle2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { InspectionFormDataType } from "@/types/inspection";
import { useState, useMemo } from "react";
import { cn } from "@/lib/utils";

interface InstallationStatusStepProps {
  formData: Partial<InspectionFormDataType>;
  updateFormData: (fieldPath: string, value: any) => void;
}

const STATUSES = [
  { 
    id: "installed", 
    label: "Installed", 
    description: "The part has been installed on a vehicle and is reported as defective",
    icon: <Activity className="h-5 w-5" />
  },
  { 
    id: "not_installed", 
    label: "Not Installed", 
    description: "The part is new/unused and has not been installed on a vehicle",
    icon: <CheckCircle2 className="h-5 w-5" />
  }
];

export function InstallationStatusStep({ 
  formData, 
  updateFormData,
}: InstallationStatusStepProps) {
  const [selectionConfirmed, setSelectionConfirmed] = useState(!!formData.installationStatus);

  const selectedStatusDescription = useMemo(() => {
    return STATUSES.find(status => status.id === formData.installationStatus)?.description;
  }, [formData.installationStatus]);

  const handleStatusChange = (value: string) => {
    updateFormData("installationStatus", value);
    setSelectionConfirmed(true);
  };

  const resetStatus = () => {
    updateFormData("installationStatus", undefined);
    setSelectionConfirmed(false);
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full flex-shrink-0">
            <Activity className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-medium">Installation Status</h3>
            <p className="text-sm text-muted-foreground">
              Specify whether the part has been installed on a vehicle.
            </p>
          </div>
        </div>
        
        <div className="flex gap-2 flex-shrink-0 self-end sm:self-center">
          {selectionConfirmed && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={resetStatus}  
              className="flex items-center gap-1 hover:bg-accent hover:text-accent-foreground"
              aria-label="Reselect installation status"
            >
              <RefreshCw className="h-3 w-3" />
              Reselect
            </Button>
          )}
        </div>
      </div>

      {!selectionConfirmed ? (
        <>
          <div className="text-sm text-blue-700 bg-blue-50 p-3 rounded-md border border-blue-200 mt-2" role="alert">
            Please select one of the following options to continue.
          </div>
          
          <RadioGroup
            value={formData.installationStatus}
            onValueChange={handleStatusChange}
            className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"
            aria-labelledby="installation-status-heading"
          >
            {STATUSES.map((status) => (
              <Label
                key={status.id}
                htmlFor={status.id}
                className={cn(
                  "relative flex cursor-pointer rounded-lg border bg-card text-card-foreground shadow-sm transition-all",
                  "hover:bg-accent hover:text-accent-foreground focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
                  formData.installationStatus === status.id && "border-primary ring-2 ring-primary ring-offset-2 bg-primary/5"
                )}
              >
                <CardContent className="p-5 flex items-start gap-4 w-full">
                  <div className={cn(
                    "mt-1 p-2 rounded-full transition-colors",
                    formData.installationStatus === status.id 
                      ? "bg-primary text-primary-foreground" 
                      : "bg-muted text-muted-foreground"
                  )}>
                    {status.icon}
                  </div>
                  <div className="flex-grow">
                    <span className="block text-base font-medium">
                      {status.label}
                    </span>
                    <span className="block text-sm text-muted-foreground mt-1">
                      {status.description}
                    </span>
                  </div>
                  <RadioGroupItem 
                    value={status.id} 
                    id={status.id} 
                    className="absolute top-4 right-4 h-5 w-5" 
                    aria-label={status.label}
                  />
                </CardContent>
              </Label>
            ))}
          </RadioGroup>
        </>
      ) : (
        <div className="bg-green-50 border border-green-200 p-4 rounded-lg mt-4 flex items-center gap-3" role="status">
          <CheckCircle2 className="h-6 w-6 text-green-600 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-green-800">Status Selected: {STATUSES.find(s => s.id === formData.installationStatus)?.label}</h4>
            {selectedStatusDescription && (
              <p className="text-sm text-green-700">
                {selectedStatusDescription}
              </p>
            )}
          </div>
        </div>
      )}


    </div>
  );
} 