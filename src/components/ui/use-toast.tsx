"use client"

import { toast as sonnerToast } from "sonner"

type ToastOptions = any;

export function useToast() {
  return {
    toast: (message: string, options?: ToastOptions) => {
      return sonnerToast(message, options)
    },
    error: (message: string, options?: ToastOptions) => {
      return sonnerToast.error(message, options)
    },
    success: (message: string, options?: ToastOptions) => {
      return sonnerToast.success(message, options)
    },
    info: (message: string, options?: ToastOptions) => {
      return sonnerToast(message, {
        ...options,
        style: {
          backgroundColor: "var(--info)",
          color: "var(--info-foreground)",
          ...options?.style,
        },
      })
    },
    warning: (message: string, options?: ToastOptions) => {
      return sonnerToast(message, {
        ...options,
        style: {
          backgroundColor: "var(--warning)",
          color: "var(--warning-foreground)",
          ...options?.style,
        },
      })
    },
  }
} 