"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Upload, X, RefreshCw, CheckCircle, FileImage, AlertTriangle } from "lucide-react";
import { isImageFile, convertHeicToPng, validateFileUpload } from "@/lib/image-utils";
import { handleImageUpload } from "@/lib/cloudflare-upload";
import imageCompression from "browser-image-compression";

// Add a flag to allow local preview mode for development
const USE_LOCAL_PREVIEW = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_LOCAL_PREVIEW === 'true';

export interface ImageUploaderProps {
  id: string;
  label: string;
  description?: string;
  buttonText?: string;
  placeholderText?: string;
  existingImage?: {
    id: string;
    url: string;
    caption: string;
    originalFileName?: string;
    convertedFileName?: string;
    ocrConfidence?: number;
    imageId?: string;
  };
  showPreview?: boolean;
  onImageUploaded: (imageData: {
    id: string;
    url: string;
    caption: string;
    originalFileName: string;
    convertedFileName?: string;
    imageId: string;
  }) => void;
  onUploadStart?: () => void;
  onUploadComplete?: () => void;
  onError?: (error: string) => void;
  onRetry?: () => void;
  onFileSelected?: (fileData: {
    file: File;
    previewUrl: string;
    originalFileName: string;
  }) => void;
  allowMultiple?: boolean;
  onRemove?: () => void;
}

export function ImageUploader({
  id,
  label,
  description,
  buttonText = "Select Image",
  placeholderText = "Upload a clear image",
  existingImage,
  showPreview = true,
  onImageUploaded,
  onUploadStart,
  onUploadComplete,
  onError,
  onRetry,
  onFileSelected,
  allowMultiple = false,
  onRemove,
}: ImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [conversionStatus, setConversionStatus] = useState<string | null>(null);
  const [blobUrls, setBlobUrls] = useState<string[]>([]);
  const [usingLocalPreview, setUsingLocalPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Cleanup object URLs when component unmounts
  useEffect(() => {
    return () => {
      blobUrls.forEach(url => {
        if (url && url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [blobUrls]);

  // If there's an existing image, show it in preview
  useEffect(() => {
    if (existingImage?.url && !imagePreview) {
      setImagePreview(existingImage.url);
    }
  }, [existingImage, imagePreview]);

  // Function to handle file upload button click
  const handleUploadButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      fileInputRef.current.click();
    }
  };

  // Function to handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    if (selectedFiles.length === 0) {
      return;
    }

    // If multi-file enabled we iterate, otherwise just process the first file
    const filesToProcess = allowMultiple ? selectedFiles : [selectedFiles[0]];

    // Reset states before batch upload
    setErrorMessage(null);
    setConversionStatus(null);
    setUsingLocalPreview(false);

    setIsUploading(true);
    if (onUploadStart) onUploadStart();

    for (const file of filesToProcess) {
      await processSingleFile(file);
    }

    setIsUploading(false);
    if (onUploadComplete) onUploadComplete();
  };

  /*
   * Processes one individual file – previously this logic lived in the
   * handleFileUpload body. It has been extracted so we can iterate over many
   * files while keeping the original flow intact.
   */
  const processSingleFile = async (file: File) => {
    try {
      // Validate file upload security constraints
      const validation = validateFileUpload(file);
      if (!validation.isValid) {
        setErrorMessage(validation.error || "Invalid file");
        if (onError) onError(validation.error || "Invalid file");
        return;
      }

      // Check for valid image type (including HEIC)
      if (!isImageFile(file)) {
        const error = "Please upload an image file (JPG, PNG, GIF, HEIC)";
        setErrorMessage(error);
        if (onError) onError(error);
        return;
      }

      let processedFile = file;
      let converted = false;
      let originalFileName = file.name;

      // Detect HEIC and convert
      const isHeicFile = file.name.toLowerCase().endsWith('.heic') ||
                        file.name.toLowerCase().endsWith('.heif');
      if (isHeicFile) {
        setConversionStatus("Converting HEIC image to PNG format...");
      }

      if (isHeicFile) {
        try {
          processedFile = await convertHeicToPng(file);
          converted = processedFile.name !== file.name;
          if (converted) {
            setConversionStatus(`Successfully converted from HEIC to PNG`);
          }
        } catch (conversionError) {
          console.error("Error converting HEIC:", conversionError);
          // Continue with original file
        }
      }

      // Compress the image
      try {
        const compressedFile = await imageCompression(processedFile, {
          maxSizeMB: 1,
          maxWidthOrHeight: 1600,
          useWebWorker: true,
        });
        processedFile = compressedFile;
      } catch (compressionError) {
        console.error('Image compression failed:', compressionError);
      }

      // Local preview
      const previewUrl = URL.createObjectURL(processedFile);
      setBlobUrls(prev => [...prev, previewUrl]);
      setImagePreview(previewUrl);

      if (onFileSelected) {
        onFileSelected({ file: processedFile, previewUrl, originalFileName });
      }

      if (USE_LOCAL_PREVIEW) {
        const fakeImageId = `local_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        onImageUploaded({
          id,
          url: previewUrl,
          caption: label || "Uploaded Image",
          originalFileName,
          convertedFileName: converted ? processedFile.name : undefined,
          imageId: fakeImageId,
        });
        return;
      }

      try {
        const formData = new FormData();
        formData.append('file', processedFile);
        const result = await handleImageUpload(formData);
        if ('error' in result) {
          console.warn("Cloudflare upload failed, using local preview:", result.error);
          const fakeImageId = `local_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          onImageUploaded({
            id,
            url: previewUrl,
            caption: label || "Uploaded Image (Local)",
            originalFileName,
            convertedFileName: converted ? processedFile.name : undefined,
            imageId: fakeImageId,
          });
          if (onError) onError(result.error.message);
        } else {
          onImageUploaded({
            id,
            url: result.displayUrl,
            caption: label || "Uploaded Image",
            originalFileName,
            convertedFileName: converted ? processedFile.name : undefined,
            imageId: result.id,
          });
        }
      } catch (uploadError) {
        console.error("Cloudflare upload error:", uploadError);
        const fakeImageId = `local_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        const errMsg = `Failed to upload to cloud: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}. Using local preview instead.`;
        onImageUploaded({
          id,
          url: previewUrl,
          caption: label || "Uploaded Image (Local)",
          originalFileName,
          convertedFileName: converted ? processedFile.name : undefined,
          imageId: fakeImageId,
        });
        if (onError) onError(errMsg);
      }
    } catch (err) {
      console.error("Error processing file:", err);
      const errorMessage = `Error: ${err instanceof Error ? err.message : 'Failed to process image'}`;
      setErrorMessage(errorMessage);
      if (onError) onError(errorMessage);
    }
  };

  // Function to remove image and reset
  const handleRemoveImage = () => {
    if (imagePreview && imagePreview.startsWith('blob:')) {
      URL.revokeObjectURL(imagePreview);
      setBlobUrls(prev => prev.filter(url => url !== imagePreview));
    }
    setImagePreview(null);
    setErrorMessage(null);
    setConversionStatus(null);
    setUsingLocalPreview(false);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    if (onRemove) {
      onRemove();
    }
  };

  return (
    <div className="space-y-4">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        id={`file-${id}`}
        className="hidden"
        accept="image/*,.heic,.HEIC"
        onChange={handleFileUpload}
        disabled={isUploading}
        {...(allowMultiple ? { multiple: true } : {})}
      />

      {/* Label and visible UI */}
      <div className="grid w-full items-center gap-1.5">
        {label && <Label htmlFor={`file-${id}`}>{label}</Label>}

        {/* Error message */}
        {errorMessage && (
          <Alert variant={usingLocalPreview ? "default" : "destructive"} className="mb-2">
            {usingLocalPreview && <AlertTriangle className="h-4 w-4 mr-2" />}
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        {/* Local preview warning */}
        {usingLocalPreview && !errorMessage && (
          <Alert className="mb-2 bg-yellow-100/80">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <AlertDescription>
              Using local preview. Image will not persist after refresh.
            </AlertDescription>
          </Alert>
        )}

        {/* Conversion status */}
        {conversionStatus && (
          <Alert className="mb-2 bg-primary/10">
            {isUploading ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            <AlertDescription>{conversionStatus}</AlertDescription>
          </Alert>
        )}

        {/* Image preview */}
        {showPreview && imagePreview && (
          <div className="relative border rounded-md overflow-hidden mb-2">
            <div className="aspect-video relative bg-muted/40 rounded-md overflow-hidden">
              <img
                src={imagePreview}
                alt={label || "Image preview"}
                className="object-contain w-full h-full"
              />

              {existingImage?.convertedFileName && (
                <Badge className="absolute bottom-2 left-2 bg-primary/90">
                  Converted from HEIC
                </Badge>
              )}

              {usingLocalPreview && (
                <Badge className="absolute bottom-2 left-2 bg-yellow-500/90">
                  Local Preview
                </Badge>
              )}

              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8"
                onClick={handleRemoveImage}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {description && (
              <p className="text-xs text-muted-foreground mt-1 px-3 pb-2">
                {description}
              </p>
            )}

            {onRetry && (
              <div className="flex justify-center py-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRetry}
                  disabled={isUploading}
                >
                  <FileImage className="h-4 w-4 mr-2" />
                  Process Image Again
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Upload button or dropzone */}
        {(!imagePreview || !showPreview) && (
          <>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="secondary"
                onClick={handleUploadButtonClick}
                disabled={isUploading}
                className="flex-1"
              >
                {isUploading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    {buttonText}
                  </>
                )}
              </Button>
            </div>

            {/* Drag and drop area for desktop */}
            <div
              className="border border-dashed rounded-md p-8 text-center text-muted-foreground cursor-pointer mt-2 hidden md:block"
              onClick={handleUploadButtonClick}
            >
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p>{placeholderText}</p>
              <p className="text-xs mt-1">
                <Badge variant="outline" className="font-normal">Supports HEIC</Badge>
              </p>
            </div>
          </>
        )}

        <p className="text-xs text-muted-foreground mt-1">
          Supported formats: JPG, PNG, GIF, HEIC (iPhone photos)
        </p>
      </div>
    </div>
  );
}