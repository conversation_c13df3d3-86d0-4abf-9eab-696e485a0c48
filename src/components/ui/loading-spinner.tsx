import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  text?: string;
  variant?: "spinner" | "dots" | "pulse";
}

export function LoadingSpinner({
  className,
  size = "md",
  text,
  variant = "spinner"
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  };

  if (variant === "dots") {
    return (
      <div className={cn("flex items-center space-x-1", className)}>
        <div className={cn("bg-current rounded-full animate-pulse", sizeClasses[size])} />
        <div className={cn("bg-current rounded-full animate-pulse", sizeClasses[size])} style={{ animationDelay: "0.2s" }} />
        <div className={cn("bg-current rounded-full animate-pulse", sizeClasses[size])} style={{ animationDelay: "0.4s" }} />
        {text && <span className="ml-2 text-sm text-muted-foreground">{text}</span>}
      </div>
    );
  }

  if (variant === "pulse") {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className={cn("bg-current rounded-full animate-pulse", sizeClasses[size])} />
        {text && <span className="text-sm text-muted-foreground animate-pulse">{text}</span>}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Loader2 className={cn("animate-spin", sizeClasses[size])} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  );
}

// Specialized loading components for common use cases
export function FormLoadingSpinner({ text = "Processing..." }: { text?: string }) {
  return (
    <div className="flex items-center justify-center py-8">
      <LoadingSpinner size="md" text={text} />
    </div>
  );
}

export function PageLoadingSpinner({ text = "Loading..." }: { text?: string }) {
  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <LoadingSpinner size="lg" text={text} />
    </div>
  );
}

export function InlineLoadingSpinner({ text }: { text?: string }) {
  return <LoadingSpinner size="sm" text={text} className="inline-flex" />;
}