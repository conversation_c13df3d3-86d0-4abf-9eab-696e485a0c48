import { FileText } from "lucide-react";
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export function SopGuideDisplay() {
  return (
    <Card className="max-w-5xl mx-auto">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-primary" />
          Standard Operating Procedure
        </CardTitle>
        <CardDescription>
          Reference guidelines for thermal components warranty inspection (SOP v1.0.0)
        </CardDescription>
      </CardHeader>
      <Separator />
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Purpose</h3>
            <p className="text-sm text-muted-foreground">
              Provide a repeatable, auditable process for field inspectors to evaluate warranty claims on‑site, 
              capture the required evidence, and generate a personal reference report (Excel) without re‑typing data.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Scope</h3>
            <p className="text-sm text-muted-foreground">
              Applicable to all warranty inspections performed by Technical Services for products installed 
              or not installed at the time of inspection. The primary interface is in English.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Process Overview</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm pl-2">
              <li>Specify installation status (Installed/Not Installed).</li>
              <li>Upload photo of the product/box label; system attempts OCR to extract details.</li>
              <li>Manually verify or enter part details (part number, serial, type, etc.). Part type is often auto-detected.</li>
              <li>Complete additional information relevant to the installation status (e.g., vehicle details or package condition).</li>
              <li>Upload required photos based on the checklist for the installation status.</li>
              <li>Complete the inspection checklist and select a preliminary decision.</li>
              <li>Review all entered data and uploaded photos for accuracy and completeness.</li>
              <li>Generate an Excel report with a standardized name (for personal reference/backup).</li>
              <li>Submit the complete inspection data (form data + photos) when online.</li>
            </ol>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Photo Requirements</h3>
            <p className="text-xs text-muted-foreground mb-2">
              The main product/box label is captured in the "Label Upload" step. The following are for the "Photo Documentation" step.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="border rounded-md p-3">
                <h4 className="font-medium mb-2">Installed Parts</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>PRODUCT_OVERVIEW (Overall condition of the installed part)</li>
                  <li>VEHICLE_REGISTRATION (Ruhsat - Vehicle registration document)</li>
                  <li>FAULT_FOCUS (Close-up of the defect, if applicable)</li>
                  <li>VIN_LABEL (Vehicle Identification Number label)</li>
                  {/* PRODUCT_LABEL is not listed as its installedRequired is false, covered by LabelUploadStep */}
                </ul>
              </div>
              
              <div className="border rounded-md p-3">
                <h4 className="font-medium mb-2">Not Installed Parts</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>PRODUCT_OVERVIEW (Overall condition of the part, possibly out of box)</li>
                  <li>FULL_BOX (Complete packaging integrity)</li>
                  <li>BOX_DAMAGE (If packaging is damaged)</li>
                  <li>PRODUCT_LABEL (On component itself, if unboxed and has a separate label)</li>
                  <li>FAULT_FOCUS (Close-up of any defect on the uninstalled part, if applicable)</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="border rounded-md p-3 bg-info/10 border-info mt-4">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <span className="bg-info text-white text-xs py-0.5 px-2 rounded-full">INFO</span>
              Key Features
            </h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>Automatic OCR for label data extraction.</li>
              <li>iPhone HEIC images automatically converted to PNG.</li>
              <li>Support for multiple photo uploads per category for comprehensive documentation.</li>
            </ul>
          </div>
          
          <div className="bg-muted/30 p-4 rounded-md">
            <h3 className="text-base font-medium mb-2">Need Help?</h3>
            <p className="text-sm text-muted-foreground">
              For assistance with this application, please contact IT Support at extension 4357 or
              <a href="mailto:<EMAIL>" className="text-primary ml-1"><EMAIL></a>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 