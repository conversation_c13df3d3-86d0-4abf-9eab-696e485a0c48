import Link from "next/link";
import { <PERSON><PERSON>ightIcon, FileText } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export function SopReferencesCard() {
  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-xl font-semibold text-slate-800 dark:text-slate-100">
          <FileText className="h-5 w-5 text-primary" />
          SOP References
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-400">
          Standard operational procedures for warranty inspections.
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ul className="space-y-3">
          {[{
            href: "/docs/warranty-process",
            label: "Warranty Inspection Process"
          }, {
            href: "/docs/photo-requirements",
            label: "Photo Requirements"
          }, {
            href: "/docs/decision-guidelines",
            label: "Warranty Decision Guidelines"
          }].map(item => (
          <li key={item.href} className="flex items-center gap-2.5 text-sm group">
            <ArrowRightIcon className="h-4 w-4 text-primary transition-transform duration-200 ease-in-out group-hover:translate-x-1 group-active:translate-x-1" />
            <Link href={item.href} className="text-slate-700 dark:text-slate-300 hover:text-primary dark:hover:text-primary-400 hover:underline underline-offset-4 transition-colors duration-200">
              {item.label}
            </Link>
          </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter className="border-t border-slate-200 dark:border-slate-700 pt-4">
        <Button asChild variant="ghost" className="w-full text-primary hover:bg-primary/10">
          <Link href="/docs">View All Documentation</Link>
        </Button>
      </CardFooter>
    </Card>
  );
} 