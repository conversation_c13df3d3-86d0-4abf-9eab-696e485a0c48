import Link from "next/link";
import { ArrowRightIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export function HeroSection() {
  return (
    <section className="w-full pt-12 sm:pt-16 md:pt-24 pb-8 sm:pb-12 md:pb-16 bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800">
      <div className="container px-4 space-y-8 sm:space-y-10 text-center">
        <div className="flex flex-col items-center gap-4">
          <Badge variant="secondary" className="px-4 py-1.5 text-sm font-medium rounded-full shadow-sm">
            Professional Field Inspection
          </Badge>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight leading-tight">
            Warranty Inspection App
          </h1>
          <p className="mx-auto max-w-[750px] text-lg text-muted-foreground md:text-xl">
            A comprehensive solution for inspecting and documenting warranty claims for thermal management parts.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            <Button asChild size="lg" className="w-full sm:w-auto px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-shadow duration-300">
              <Link href="/inspection">
                Start New Inspection <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="w-full sm:w-auto px-8 py-3 text-base font-semibold shadow-sm hover:shadow-md transition-shadow duration-300">
              <Link href="/docs">
                Read SOP Documentation
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
} 