import Link from "next/link";
import { AlertTriangle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";

export function ImportantNoticeCard() {
  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl bg-amber-50 dark:bg-amber-900/60 border-amber-400 dark:border-amber-600/90">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-amber-700 dark:text-amber-300">
          <AlertTriangle className="h-5 w-5" />
          Important Notice
        </CardTitle>
      </CardHeader>
      <CardContent className="text-amber-700 dark:text-amber-400 text-[0.9rem] leading-relaxed pt-0 max-w-prose mx-auto">
        <p>Remember to complete all required photos for each inspection and verify OCR-extracted data before submission. Warranty team review takes 1-2 business days.</p>
      </CardContent>
      <CardFooter className="border-t border-amber-300 dark:border-amber-700 pt-4">
        <Button
          asChild
          variant="secondary"
          className="w-full text-amber-800 dark:text-amber-200 hover:bg-amber-100 dark:hover:bg-amber-800/70 transition-colors duration-200 shadow-sm"
        >
          <Link href="/docs/best-practices">View Tips & Best Practices</Link>
        </Button>
      </CardFooter>
    </Card>
  );
} 