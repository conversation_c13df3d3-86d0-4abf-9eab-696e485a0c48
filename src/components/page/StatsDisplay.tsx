import { BarChart3 } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>it<PERSON>,
} from "@/components/ui/card";

interface StatItem {
  label: string;
  value: number | string;
}

interface StatsDisplayProps {
  stats: StatItem[];
}

export function StatsDisplay({ stats }: StatsDisplayProps) {
  return (
    <section className="container w-full max-w-5xl py-8 sm:py-10 grid gap-6 sm:grid-cols-2 lg:grid-cols-4 sm:gap-8">
      {stats.map((stat, index) => (
        <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-base font-semibold text-muted-foreground">
              {stat.label}
            </CardTitle>
            <BarChart3 className="h-5 w-5 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-slate-800 dark:text-slate-100 whitespace-nowrap">{stat.value}</div>
          </CardContent>
        </Card>
      ))}
    </section>
  );
} 