import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface InspectionItem {
  id: string;
  date: string;
  part: string;
  decision: string;
  status: string;
}

interface RecentInspectionsCardProps {
  inspections: InspectionItem[];
}

export function RecentInspectionsCard({ inspections }: RecentInspectionsCardProps) {
  return (
    <Card className="col-span-full md:col-span-2 shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-slate-800 dark:text-slate-100">Recent Inspections</CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-400">
          Your most recent warranty inspections and their status.
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {inspections.map((inspection: InspectionItem) => (
            <div key={inspection.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-4 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors duration-200">
              <div className="grid gap-0.5">
                <Link href={`/inspections/${inspection.id}`} className="font-semibold text-primary hover:underline">
                  {inspection.id}
                </Link>
                <div className="text-sm text-muted-foreground flex items-center gap-2">
                  <span>{new Date(inspection.date).toLocaleDateString()}</span>
                  <span className="text-muted-foreground/40">•</span>
                  <span className="capitalize">{inspection.part}</span>
                </div>
              </div>
              <div className="flex items-center gap-3 mt-2 sm:mt-0">
                <Badge
                  variant={
                    inspection.decision === "Warranty" ? "default" :
                    inspection.decision === "Goodwill" ? "secondary" :
                    "destructive"
                  }
                  className="px-2.5 py-1 text-xs font-medium rounded-full shadow-sm"
                >
                  {inspection.decision}
                </Badge>
                <Badge
                  variant={inspection.status === "Pending" ? "outline" : "default"}
                  className={`px-2.5 py-1 text-xs font-medium rounded-full shadow-sm ${ 
                    inspection.status === "Approved" ? 
                    "bg-green-100 text-green-700 border-green-300 dark:bg-green-800/30 dark:text-green-300 dark:border-green-600" :
                    inspection.status === "Pending" ? 
                    "bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-800/30 dark:text-amber-300 dark:border-amber-600" :
                     "bg-slate-100 text-slate-700 border-slate-300 dark:bg-slate-700 dark:text-slate-300 dark:border-slate-600" 
                  }`}
                >
                  {inspection.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="border-t border-slate-200 dark:border-slate-700 pt-4">
        <Button asChild variant="ghost" className="w-full text-primary hover:bg-primary/10">
          <Link href="/inspections">View All Inspections</Link>
        </Button>
      </CardFooter>
    </Card>
  );
} 