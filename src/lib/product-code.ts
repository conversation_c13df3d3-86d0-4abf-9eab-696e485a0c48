export const PRODUCT_COMPONENT_MAP: Record<string, string> = {
  AA: "A/C actuator",
  AB: "Interior blower",
  ABI: "Blower impeller",
  ABM: "E-Motor, interior blower",
  ABR: "A/C blower regulator and resistor",
  AC: "A/C condenser",
  ACH: "Chiller",
  ACF: "A/C condenser fan",
  ACP: "A/C compressor",
  ACPX: "A/C compressor spare parts",
  ACPL: "A/C compressor oil",
  AD: "Filter-drier and accumulator",
  AE: "Evaporator",
  AH: "Interior heat exchanger",
  AHX: "Interior heat exchanger spare parts",
  AP: "A/C pipes and hoses",
  APX: "A/C pipes and hoses spare parts",
  ASE: "A/C sensor",
  ASW: "A/C switch",
  AVE: "Expansion valve and orifice tube",
  AVS: "A/C solenoid valve",
  AX: "A/C spare parts",
  CE: "EGR-cooler",
  CFF: "Radiator/condenser fan",
  CI: "Charge air cooler",
  CK: "Fuel cooler",
  CFB: "Battery fan",
  CFC: "Radiator fan clutch",
  CFM: "E-motor, radiator/condenser fan",
  CFR: "Engine cooling fan regulator + resistor",
  CFS: "Fan shroud",
  CFW: "Fan wheel",
  CFX: "Fan spare parts",
  CIC: "Charge air cooler core",
  CIR: "Low-temperature radiator",
  CIX: "Charge air cooler spare parts",
  CM: "Cooling module",
  CLC: "Oil cooler",
  CP: "Water pump",
  CPK: "Water pump kit",
  CPKX: "Water pump kit spare parts",
  CR: "Radiator",
  CRB: "Radiator cap",
  CRC: "Radiator core",
  CRX: "Radiator spare parts",
  CRT: "Expansion tank",
  CRTX: "Expansion tank spare parts",
  CRTC: "Expansion",
  CV: "Cooling valve",
  CX: "Cooling system spare parts",
};

export type QualityGrade = "premium" | "standard" | null;

export interface ParsedProductCode {
  componentCode: string;
  componentName: string | undefined;
  quality: QualityGrade;
  identifierDigits?: string;
  fullCode: string;
}

/**
 * Parse a product code such as "CR 220 000P" or "AC 123 000S".
 * Returns component information and quality grade.
 * 
 * Format:
 * - First part: Component code (1-4 letters like CR, AC, CRT)
 * - Middle part: Identifier digits (usually 3-4 digits)
 * - Last part: Always ends with 000P (Premium) or 000S (Standard)
 */
export function parseProductCode(codeRaw: string): ParsedProductCode | null {
  if (!codeRaw) return null;
  
  // Normalize code: trim whitespace, uppercase, normalize spaces
  const code = codeRaw.trim().toUpperCase().replace(/\s+/g, " ");
  
  // Match pattern: [LETTERS] [DIGITS] [000P/000S]
  const regex = /^([A-Z]{1,4})\s+(\d{2,4})\s+(000[PS])$/;
  const match = code.match(regex);
  
  if (!match) {
    // Try alternative format with no spaces
    const compactRegex = /^([A-Z]{1,4})(\d{2,4})(000[PS])$/;
    const compactMatch = code.match(compactRegex);
    
    if (!compactMatch) {
      // If still no match, use basic extraction
      return basicExtraction(code);
    }
    
    // Destructure compact match
    const [_, componentCode, identifierDigits, qualitySuffix] = compactMatch;
    return createResult(componentCode, identifierDigits, qualitySuffix, code);
  }
  
  // Destructure match
  const [_, componentCode, identifierDigits, qualitySuffix] = match;
  return createResult(componentCode, identifierDigits, qualitySuffix, code);
}

/**
 * Creates a structured result from parsed components
 */
function createResult(
  componentCode: string,
  identifierDigits: string,
  qualitySuffix: string,
  fullCode: string
): ParsedProductCode {
  const componentName = PRODUCT_COMPONENT_MAP[componentCode];
  
  // Determine quality from suffix
  let quality: QualityGrade = null;
  if (qualitySuffix.endsWith('P')) {
    quality = "premium";
  } else if (qualitySuffix.endsWith('S')) {
    quality = "standard";
  }
  
  return {
    componentCode,
    componentName,
    identifierDigits,
    quality,
    fullCode
  };
}

/**
 * Basic extraction for formats that don't match the expected pattern
 */
function basicExtraction(code: string): ParsedProductCode | null {
  // Component code = first token (1-4 letters)
  const parts = code.split(" ");
  if (!parts.length) return null;
  
  const componentCode = parts[0].match(/^[A-Z]{1,4}/)?.[0] || "";
  if (!componentCode) return null;
  
  const componentName = PRODUCT_COMPONENT_MAP[componentCode];
  
  // Quality grade based on last char P or S
  let quality: QualityGrade = null;
  const lastChar = code[code.length - 1];
  if (lastChar === "P") quality = "premium";
  else if (lastChar === "S") quality = "standard";
  
  return { 
    componentCode, 
    componentName, 
    quality,
    fullCode: code
  };
} 