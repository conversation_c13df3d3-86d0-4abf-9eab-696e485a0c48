import { z } from "zod";

// Define a schema for the form - some fields may not be in the database schema
export const InspectionFormSchema = z.object({
  // language field is for UI only, not stored in database 
  language: z.string().default("en"),
  partType: z.string().default(""),
  installationStatus: z.enum(["installed", "not_installed"]).optional(),
  partDetails: z.object({
    partNumber: z.string().min(1, "Part number is required"),
    productCode: z.string().default(""),
    serialNumber: z.string().default(""),
    expiryDate: z.string().default(""),
    manufacturer: z.string().default(""),
    qualityGrade: z.enum(["premium", "standard"]).default("standard"),
    vehicleCompatibility: z.string().default(""),
  }),
  vehicleInfo: z.object({
    make: z.string().default(""),
    model: z.string().default(""),
    vinNumber: z.string().default(""),
    mileage: z.string().default(""),
    installationDate: z.string().default(""),
  }).default({}),
  uninstalledInfo: z.object({
    boxCondition: z.string().default(""),
    hasPackageDamage: z.boolean().default(false),
    storedProperly: z.boolean().default(false),
    sealIntact: z.boolean().default(false),
    accessoriesPresent: z.boolean().default(false),
    reasonForClaim: z.string().default(""),
  }).default({}),
  installedChecklist: z.object({
    flushPerformed: z.boolean().default(false),
    correctCoolant: z.boolean().default(false),
    thermostatReplaced: z.boolean().default(false),
  }).default({}),
  photos: z.array(z.object({
    id: z.string(),
    url: z.string().url(),
    caption: z.string(),
    ocrConfidence: z.number().optional(),
    group: z.string().optional(),
    originalFileName: z.string().optional(),
    convertedFileName: z.string().optional(),
    imageId: z.string().optional(),
  })).default([]),
  decision: z.string().default(""),
  notes: z.string().default(""),
});

export type InspectionFormType = z.infer<typeof InspectionFormSchema>; 