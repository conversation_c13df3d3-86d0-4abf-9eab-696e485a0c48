import { z } from "zod";

// File validation constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];

// Enhanced photo schema with security validation
const PhotoSchema = z.object({
  id: z.string().min(1, "Photo ID is required"),
  url: z.string().url("Invalid photo URL").refine(
    (url) => {
      // Ensure URL is from allowed domains (Cloudflare or local development)
      const allowedDomains = [
        process.env.CLOUDFLARE_DOMAIN,
        'localhost',
        '127.0.0.1',
        'blob:'
      ].filter(Boolean);

      return allowedDomains.some(domain => url.includes(domain as string));
    },
    "Photo URL must be from an allowed domain"
  ),
  caption: z.string().min(1, "Photo caption is required").max(200, "Caption too long"),
  ocrConfidence: z.number().min(0).max(100).optional(),
  group: z.string().optional(),
  originalFileName: z.string().optional(),
  convertedFileName: z.string().optional(),
  imageId: z.string().optional(),
});

// Define a schema for the form - some fields may not be in the database schema
export const InspectionFormSchema = z.object({
  // language field is for UI only, not stored in database
  language: z.string().default("en"),
  partType: z.string().default(""),
  installationStatus: z.enum(["installed", "not_installed"]).optional(),
  partDetails: z.object({
    partNumber: z.string().min(1, "Part number is required"),
    productCode: z.string().default(""),
    serialNumber: z.string().default(""),
    expiryDate: z.string().default(""),
    manufacturer: z.string().default(""),
    qualityGrade: z.enum(["premium", "standard"]).default("standard"),
    vehicleCompatibility: z.string().default(""),
  }),
  vehicleInfo: z.object({
    make: z.string().default(""),
    model: z.string().default(""),
    vinNumber: z.string().default(""),
    mileage: z.string().default(""),
    installationDate: z.string().default(""),
  }).default({}),
  uninstalledInfo: z.object({
    boxCondition: z.string().default(""),
    hasPackageDamage: z.boolean().default(false),
    storedProperly: z.boolean().default(false),
    sealIntact: z.boolean().default(false),
    accessoriesPresent: z.boolean().default(false),
    reasonForClaim: z.string().default(""),
  }).default({}),
  installedChecklist: z.object({
    flushPerformed: z.boolean().default(false),
    correctCoolant: z.boolean().default(false),
    thermostatReplaced: z.boolean().default(false),
  }).default({}),
  photos: z.array(PhotoSchema).default([]),
  decision: z.string().default(""),
  notes: z.string().default(""),
});

export type InspectionFormType = z.infer<typeof InspectionFormSchema>;

// Server-side validation schema with stricter rules
export const InspectionServerSchema = InspectionFormSchema.extend({
  // Ensure part type is selected
  partType: z.string().min(1, "Part type is required"),

  // Ensure installation status is set
  installationStatus: z.enum(["installed", "not_installed"], {
    required_error: "Installation status is required"
  }),

  // Enhanced part details validation
  partDetails: z.object({
    partNumber: z.string().min(1, "Part number is required").max(50, "Part number too long"),
    productCode: z.string().max(50, "Product code too long").default(""),
    serialNumber: z.string().max(50, "Serial number too long").default(""),
    expiryDate: z.string().default(""),
    manufacturer: z.string().max(100, "Manufacturer name too long").default(""),
    qualityGrade: z.enum(["premium", "standard"]).default("standard"),
    vehicleCompatibility: z.string().max(200, "Vehicle compatibility too long").default(""),
  }),

  // Conditional validation for vehicle info (required if installed)
  vehicleInfo: z.object({
    make: z.string().max(50, "Vehicle make too long").default(""),
    model: z.string().max(50, "Vehicle model too long").default(""),
    vinNumber: z.string().max(17, "VIN number too long").default(""),
    mileage: z.string().max(20, "Mileage too long").default(""),
    installationDate: z.string().default(""),
  }).default({}),

  // Enhanced notes validation
  notes: z.string().max(1000, "Notes too long").default(""),

  // Ensure decision is made
  decision: z.string().min(1, "Decision is required"),
}).refine(
  (data) => {
    // If installed, require vehicle make and model
    if (data.installationStatus === "installed") {
      return data.vehicleInfo.make.length > 0 && data.vehicleInfo.model.length > 0;
    }
    return true;
  },
  {
    message: "Vehicle make and model are required for installed parts",
    path: ["vehicleInfo"]
  }
).refine(
  (data) => {
    // If not installed, require uninstalled info
    if (data.installationStatus === "not_installed") {
      return data.uninstalledInfo.boxCondition.length > 0;
    }
    return true;
  },
  {
    message: "Box condition is required for uninstalled parts",
    path: ["uninstalledInfo"]
  }
);

export type InspectionServerType = z.infer<typeof InspectionServerSchema>;