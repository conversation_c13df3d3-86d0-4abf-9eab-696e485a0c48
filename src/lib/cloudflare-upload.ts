"use server";

export type UploadError = {
  code:
    | "INVALID_FILE_TYPE"
    | "FILE_TOO_LARGE"
    | "UPLOAD_FAILED"
    | "INVALID_CREDENTIALS"
    | "NETWORK_ERROR";
  message: string;
};

// Helper function to handle Cloudflare response
async function handleCloudflareResponse(response: Response) {
  if (!response.ok) {
    const errorText = await response.text();
    console.error("Upload response error:", {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
    });

    return {
      error: {
        code: "UPLOAD_FAILED" as const,
        message:
          response.status === 413
            ? "Image size is too large"
            : "Failed to upload image. Please try again.",
      },
    };
  }

  const result = await response.json();
  console.log("Cloudflare API response:", JSON.stringify(result, null, 2));

  if (!result.success || !result.result?.variants?.[0]) {
    console.error("Invalid upload response:", result);
    return {
      error: {
        code: "UPLOAD_FAILED" as const,
        message: "Failed to process image. Please try again.",
      },
    };
  }

  // Extract image ID and construct CDN URLs
  const imageId = result.result.id;
  const accountHash = process.env.NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_HASH;
  const displayUrl = `https://imagedelivery.net/${accountHash}/${imageId}/public`;
  const downloadUrl = `https://imagedelivery.net/${accountHash}/${imageId}/original`;
  console.log("Generated CDN URLs:", { displayUrl, downloadUrl });

  return { id: imageId, displayUrl, downloadUrl };
}

// Helper function to check credentials
function checkCredentials():
  | { accountId: string; apiToken: string }
  | { error: UploadError } {
  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;
  const apiToken = process.env.CLOUDFLARE_API_TOKEN;

  if (!accountId || !apiToken) {
    return {
      error: {
        code: "INVALID_CREDENTIALS",
        message: "Unable to connect to image service. Please try again later.",
      },
    };
  }

  return { accountId, apiToken };
}

export async function handleImageUpload(
  formData: FormData
): Promise<{ id: string; displayUrl: string; downloadUrl: string } | { error: UploadError }> {
  try {
    const credentials = checkCredentials();
    if ("error" in credentials) return credentials;

    // Log the file type being uploaded for debugging
    const file = formData.get('file') as File;
    if (file) {
      console.log("Uploading file:", {
        name: file.name,
        type: file.type,
        size: file.size
      });
    }

    const uploadResponse = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${credentials.accountId}/images/v1`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${credentials.apiToken}`,
        },
        body: formData,
      }
    );

    // Special handling for MIME type errors
    if (uploadResponse.status === 415) {
      const errorData = await uploadResponse.json();
      console.error("MIME type error:", errorData);
      
      return {
        error: {
          code: "INVALID_FILE_TYPE",
          message: "Unsupported file format. Please upload a JPEG, PNG, WebP, GIF, or SVG image."
        }
      };
    }

    return handleCloudflareResponse(uploadResponse);
  } catch (error) {
    console.error("Image upload error:", error);
    return {
      error: {
        code: "NETWORK_ERROR",
        message:
          "Network error occurred. Please check your connection and try again.",
      },
    };
  }
}

export async function handleUrlUpload(imageUrl: string, folderPath?: string): Promise<
  { id: string; displayUrl: string; downloadUrl: string } | { error: UploadError }
> {
  try {
    const credentials = checkCredentials();
    if ("error" in credentials) return credentials;

    const formData = new FormData();
    formData.append("url", imageUrl);

    if (folderPath) {
      formData.append("metadata", JSON.stringify({ folder: folderPath }));
    }
    formData.append("requireSignedURLs", "false");

    const uploadResponse = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${credentials.accountId}/images/v1`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${credentials.apiToken}`,
        },
        body: formData,
      }
    );

    return handleCloudflareResponse(uploadResponse);
  } catch (error) {
    console.error("URL upload error:", error);
    return {
      error: {
        code: "NETWORK_ERROR",
        message:
          "Network error occurred. Please check your connection and try again.",
      },
    };
  }
} 