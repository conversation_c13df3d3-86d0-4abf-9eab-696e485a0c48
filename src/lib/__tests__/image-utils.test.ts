import { validateFileUpload, ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from '../image-utils'

describe('validateFileUpload', () => {
  // Helper function to create a mock file
  const createMockFile = (
    name: string,
    type: string,
    size: number = 1024 * 1024 // 1MB default
  ): File => {
    const file = new File([''], name, { type })
    Object.defineProperty(file, 'size', { value: size })
    return file
  }

  describe('file type validation', () => {
    it('should accept valid image types', () => {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif']
      
      validTypes.forEach(type => {
        const file = createMockFile('test.jpg', type)
        const result = validateFileUpload(file)
        expect(result.isValid).toBe(true)
        expect(result.error).toBeUndefined()
      })
    })

    it('should reject invalid file types', () => {
      const invalidTypes = ['application/pdf', 'text/plain', 'video/mp4', 'application/exe']
      
      invalidTypes.forEach(type => {
        const file = createMockFile('test.pdf', type)
        const result = validateFileUpload(file)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('File type')
        expect(result.error).toContain('is not allowed')
      })
    })
  })

  describe('file size validation', () => {
    it('should accept files within size limit', () => {
      const file = createMockFile('test.jpg', 'image/jpeg', MAX_FILE_SIZE - 1)
      const result = validateFileUpload(file)
      expect(result.isValid).toBe(true)
    })

    it('should reject files exceeding size limit', () => {
      const file = createMockFile('test.jpg', 'image/jpeg', MAX_FILE_SIZE + 1)
      const result = validateFileUpload(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File size')
      expect(result.error).toContain('exceeds the maximum')
    })

    it('should provide correct size information in error message', () => {
      const oversizeFile = createMockFile('test.jpg', 'image/jpeg', 15 * 1024 * 1024) // 15MB
      const result = validateFileUpload(oversizeFile)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('15.0MB')
      expect(result.error).toContain('10MB')
    })
  })

  describe('suspicious file name validation', () => {
    it('should reject files with suspicious extensions', () => {
      const suspiciousNames = [
        'malware.exe.jpg',
        'virus.bat.png',
        'trojan.cmd.heic',
        'script.scr.jpeg'
      ]

      suspiciousNames.forEach(name => {
        const file = createMockFile(name, 'image/jpeg')
        const result = validateFileUpload(file)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('suspicious patterns')
      })
    })

    it('should accept normal file names', () => {
      const normalNames = [
        'photo.jpg',
        'image.png',
        'document-scan.heic',
        'IMG_1234.jpeg'
      ]

      normalNames.forEach(name => {
        const file = createMockFile(name, 'image/jpeg')
        const result = validateFileUpload(file)
        expect(result.isValid).toBe(true)
      })
    })
  })

  describe('edge cases', () => {
    it('should handle files at exact size limit', () => {
      const file = createMockFile('test.jpg', 'image/jpeg', MAX_FILE_SIZE)
      const result = validateFileUpload(file)
      expect(result.isValid).toBe(true)
    })

    it('should handle empty file names', () => {
      const file = createMockFile('', 'image/jpeg')
      const result = validateFileUpload(file)
      expect(result.isValid).toBe(true)
    })

    it('should handle case-insensitive suspicious patterns', () => {
      const file = createMockFile('test.EXE.jpg', 'image/jpeg')
      const result = validateFileUpload(file)
      expect(result.isValid).toBe(false)
    })
  })
})
