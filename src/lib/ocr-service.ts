"use server";

import { createHash } from "crypto";
import { AzureOpenAI } from "openai";
import sharp from 'sharp';
import { parseProductCode } from "./product-code";
import { serverEnv, features, isDevelopment } from "./env";

// Define the types for extracted product info
export type ExtractedPartData = {
  ocrType: "boxLabel" | "partLabel";
  partNumber: string;
  productCode?: string;
  serialNumber?: string;
  productType?: string;
  manufacturer?: string;
  vehicleCompatibility?: string;
  qualityGrade?: "premium" | "standard";
  expiryDate?: string;
  confidence: number;
};

// Define type for extracted vehicle registration ("Ruhsat") info
export type ExtractedVehicleRegistrationData = {
  ocrType: "vehicleRegistration";
  licensePlate?: string;
  ownerName?: string;
  vin?: string;
  make?: string;
  model?: string;
  firstRegistrationDate?: string;
  documentDate?: string;
  confidence: number;
};

// Define type for extracted VIN info
export type ExtractedVinData = {
  ocrType: "vin";
  vin: string;
  confidence: number;
};

// Union type for OCR results
export type OcrResult = ExtractedPartData | ExtractedVehicleRegistrationData | ExtractedVinData;

// Define possible OCR purposes
export type OcrPurpose = "boxLabel" | "partLabel" | "vehicleRegistration" | "vin";

/**
 * Convert image to base64
 */
async function imageToBase64(imageData: string): Promise<string | null> {
  try {
    // If it's already a URL, return it as is
    if (imageData.startsWith('http')) {
      return imageData;
    }

    // If it's already a base64 string, clean it up
    if (imageData.includes('base64,')) {
      return imageData;
    }

    // Otherwise, convert buffer to base64
    // It's crucial that imageData is a valid base64 string representing an image here.
    const imageBuffer = Buffer.from(imageData, 'base64');
    try {
      const processedBuffer = await sharp(imageBuffer).png().toBuffer();
      return `data:image/png;base64,${processedBuffer.toString('base64')}`;
    } catch (sharpError) {
      console.error('Error processing image with sharp:', sharpError);
      console.error('Original imageData (first 50 chars):', imageData.substring(0, 50));
      return null; // Explicitly return null if sharp fails
    }
  } catch (error) {
    // This outer catch handles errors like Buffer.from failing if imageData is not valid base64
    console.error('Error converting image (pre-sharp):', error);
    console.error('Original imageData (first 50 chars):', imageData.substring(0, 50));
    return null;
  }
}

/**
 * Initialize Azure OpenAI client based on configuration
 */
function getAzureOpenAIClient() {
  if (!features.enableOCR) {
    throw new Error('OCR feature is not enabled. Please configure Azure OpenAI credentials.');
  }

  const apiVersion = process.env.AZURE_OPENAI_API_VERSION || "2025-01-01-preview";

  return new AzureOpenAI({
    endpoint: serverEnv.AZURE_OPENAI_ENDPOINT!,
    apiKey: serverEnv.AZURE_OPENAI_API_KEY!,
    apiVersion: apiVersion
  });
}

/**
 * Generate system prompt based on OCR purpose
 * @param purpose The purpose of the OCR task
 * @returns The system prompt string
 */
function getSystemPrompt(purpose: OcrPurpose): string {
  switch (purpose) {
    case "boxLabel":
    case "partLabel":
      return `
      You are an expert OCR system that specializes in analyzing automotive part labels.
      Analyze the image and extract the following information:

      1. Part Number (format usually like "XX 123 000P" where XX is product code like CR, AC, etc., followed by digits, and ending with 000P or 000S)
      2. Product Code (if visible, often a secondary number like "300 150 200" or "123FC 456 789")
      3. Serial Number (typically a long numeric string)
      4. Product Type (e.g., A/C Condenser, A/C Compressor, Filter)
      5. Manufacturer (e.g., MAHLE BEHR)
      6. Vehicle Compatibility (e.g., BUICK REGAL, OPEL VECTRA)
      7. Quality Grade (premium if part number ends with P, standard if ends with S)
      8. Expiry Date (in format YYYYMMDD if visible)

      For part numbers, look for patterns like "CR 123 000P" or "AC 456 000S" where:
      - First part is a 1-4 letter product code (CR, AC, CRT, etc.)
      - Middle part is a 2-4 digit identifier
      - Last part always ends with 000P (Premium) or 000S (Standard)

      Provide your response as a valid JSON object. Include an "ocrType" field set to "${purpose}".
      If you cannot determine a value, use null.
      Include a "confidence" field (0-100) indicating your overall confidence in the extraction.

      Example response format for ${purpose}:
      {
        "ocrType": "${purpose}",
        "partNumber": "AC 123 000P",
        "productCode": "300 150 200",
        "serialNumber": "**********",
        "productType": "A/C Condenser",
        "manufacturer": "MAHLE BEHR",
        "vehicleCompatibility": "BUICK REGAL",
        "qualityGrade": "premium",
        "expiryDate": "20250530",
        "confidence": 85
      }
      `;
    case "vehicleRegistration":
      return `
      You are an expert OCR system specializing in analyzing Turkish Vehicle Registration documents (Trafik Tescil Belgesi / Ruhsat).
      Analyze the image provided and extract the following information:

      1. License Plate (A. Plaka No)
      2. Owner Name (C.1.1 Soyadı / C.1.2 Adı or C.4.c)
      3. VIN (E. Şasi No)
      4. Make (D.1 Markası)
      5. Model (D.3 Ticari Adı)
      6. First Registration Date (I. Tescil Tarihi, format DD.MM.YYYY)
      7. Document Date (Belge Tarihi or similar, format DD.MM.YYYY)

      Provide your response as a valid JSON object. Include an "ocrType" field set to "vehicleRegistration".
      Combine first name and last name into the "ownerName" field.
      If you cannot determine a value, use null.
      Include a "confidence" field (0-100) indicating your overall confidence in the extraction.

      Example response format:
      {
        "ocrType": "vehicleRegistration",
        "licensePlate": "34 ABC 123",
        "ownerName": "Ahmet Yılmaz",
        "vin": "WVWZZZ1KZ8P123456",
        "make": "VOLKSWAGEN",
        "model": "PASSAT",
        "firstRegistrationDate": "15.03.2021",
        "documentDate": "20.04.2023",
        "confidence": 90
      }
      `;
    case "vin":
      return `
      You are an expert OCR system specializing in extracting Vehicle Identification Numbers (VIN).
      Analyze the image and extract only the VIN (usually a 17-character alphanumeric code).
      Look for labels on the car body (doorjamb, dashboard) or potentially on documents.

      Provide your response as a valid JSON object. Include an "ocrType" field set to "vin".
      The JSON object should contain only "ocrType", "vin", and "confidence".
      If you cannot determine the VIN, use null for the "vin" field.
      Include a "confidence" field (0-100) indicating your confidence in the extracted VIN.

      Example response format:
      {
        "ocrType": "vin",
        "vin": "1GEXP**********AB",
        "confidence": 95
      }
      `;
    default:
      throw new Error(`Unsupported OCR purpose: ${purpose}`);
  }
}

/**
 * Process image with Azure OpenAI Vision models and extract information based on purpose
 * @param imageData Base64 encoded image or image URL
 * @param ocrPurpose The type of information to extract ("boxLabel", "partLabel", "vehicleRegistration", "vin")
 * @returns Extracted data object (type depends on ocrPurpose) or null on failure
 */
export async function processImageOCR(imageData: string, ocrPurpose: OcrPurpose): Promise<OcrResult | null> {
  // Validate ocrPurpose at the beginning
  if (!ocrPurpose || !["boxLabel", "partLabel", "vehicleRegistration", "vin"].includes(ocrPurpose)) {
    console.error(`Invalid or undefined ocrPurpose: ${ocrPurpose}. Aborting OCR processing.`);
    // Optionally, you could throw an error here or return a specific error object
    // For now, returning null to match existing failure patterns.
    return null;
  }

  try {
    // Get the model to use (either o1 or gpt-4o)
    const model = serverEnv.AZURE_OPENAI_VISION_MODEL || "o1";

    try {
      // Initialize the client
      const client = getAzureOpenAIClient();

      // Convert image to the right format for the API
      const processedImage = await imageToBase64(imageData);
      if (!processedImage) {
        console.warn(`Failed to process image for OCR purpose: ${ocrPurpose}`);
        return null;
      }

      // Get the appropriate system prompt based on the purpose
      const systemPrompt = getSystemPrompt(ocrPurpose);

      let result;

      // Choose the appropriate API call based on the model
      if (model.toLowerCase() === "o1") {
        // O1 model API structure
        result = await client.chat.completions.create({
          model: "o1",
          messages: [
            { role: "system", content: systemPrompt },
            {
              role: "user",
              content: [
                {
                  type: "image_url",
                  image_url: {
                    url: processedImage
                  }
                },
                {
                  type: "text",
                  text: `Analyze this image for ${ocrPurpose} information and extract the structured data as specified.`
                }
              ]
            }
          ],
          response_format: { type: "json_object" },
          max_tokens: 4000
        });
      } else {
        // GPT-4o model API structure
        result = await client.chat.completions.create({
          model: model, // "gpt-4o" or other configured model
          messages: [
            {
              role: "system",
              content: [
                {
                  type: "text",
                  text: systemPrompt
                }
              ]
            },
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: `Analyze this image for ${ocrPurpose} information and extract the structured data as specified.`
                },
                {
                  type: "image_url",
                  image_url: {
                    url: processedImage
                  }
                }
              ]
            }
          ],
          response_format: { type: "json_object" },
          max_tokens: 4000,
          temperature: 0.2
        });
      }

      // Parse the response
      try {
        const content = result.choices[0]?.message?.content || '';

        // Only log in development mode to avoid exposing sensitive data
        if (isDevelopment()) {
          console.log(`OCR response received for ${ocrPurpose}`);
        }

        const extractedData = JSON.parse(content) as OcrResult;

        // Basic validation based on type
        if (extractedData.ocrType !== ocrPurpose) {
           console.warn(`Mismatched OCR type: expected ${ocrPurpose}, got ${extractedData.ocrType}.`);
           return null;
        }

        // Ensure required fields exist based on type
        if ((ocrPurpose === "boxLabel" || ocrPurpose === "partLabel") && !(extractedData as ExtractedPartData).partNumber) {
          console.warn(`No part number found in ${ocrPurpose} OCR response`);
          return null;
        }
        if (ocrPurpose === "vin" && !(extractedData as ExtractedVinData).vin) {
           console.warn('No VIN found in VIN OCR response');
           return null;
        }
        // Add more validation for vehicleRegistration if needed

        // Ensure confidence exists and is in the right range
        if (typeof extractedData.confidence !== 'number' || extractedData.confidence < 0 || extractedData.confidence > 100) {
          extractedData.confidence = 70; // Default confidence
        }

        return extractedData;
      } catch (error) {
        console.error(`Error parsing Azure OpenAI response for ${ocrPurpose}:`, error);
        return null;
      }
    } catch (error) {
      console.error(`Error processing image with Azure OpenAI for ${ocrPurpose}:`, error);
      return null;
    }
  } catch (error) {
    console.error(`Fatal error in OCR processing for ${ocrPurpose}:`, error);
    return null;
  }
}

/**
 * Extract part data from OCR text
 */
function extractPartDataFromText(text: string): Omit<ExtractedPartData, 'confidence'> {
  // Initialize with empty values
  const data: Omit<ExtractedPartData, 'confidence'> = {
    ocrType: "partLabel",
    partNumber: '',
  };

  // Convert text to lowercase and split by lines for easier processing
  const lines = text.split('\n').map(line => line.trim());

  // Extract part number using the standard format like "XX 123 000P" or "XX 123 000S"
  const partNumberRegex = /[A-Z]{1,4}\s?\d{2,4}\s?000[PS]/i;
  const partNumberMatch = text.match(partNumberRegex);
  if (partNumberMatch) {
    data.partNumber = partNumberMatch[0];

    // Parse product code using the utility function
    const parsedCode = parseProductCode(data.partNumber);
    if (parsedCode) {
      // Set quality grade from parsed code
      data.qualityGrade = parsedCode.quality || undefined;

      // Set product type based on component name
      if (parsedCode.componentName) {
        data.productType = parsedCode.componentName;
      }
    }
  }

  // Legacy part number extraction as fallback
  if (!data.partNumber) {
    const legacyPartNumberRegex = /[A-Z]{1,3}\s?\d{3}\s?\d{4}/i;
    const legacyPartNumberMatch = text.match(legacyPartNumberRegex);
    if (legacyPartNumberMatch) {
      data.partNumber = legacyPartNumberMatch[0];
    }
  }

  // Extract secondary part number/product code (typically after the first part number)
  const productCodeRegex = /\d{1,3}FC\s?\d{3}\s?\d{3}\s?\d{3}/i;
  const productCodeMatch = text.match(productCodeRegex);
  if (productCodeMatch) {
    data.productCode = productCodeMatch[0];
  }

  // Extract product type if not already set from component code
  if (!data.productType) {
    if (text.includes('condenser') || text.includes('Kondensator')) {
      data.productType = 'A/C Condenser';
    } else if (text.includes('compressor') || text.includes('Kompressor')) {
      data.productType = 'A/C Compressor';
    } else if (text.includes('filter') || text.includes('Filter')) {
      data.productType = 'Filter';
    } else if (text.includes('radiator') || text.includes('Kühler')) {
      data.productType = 'Radiator';
    } else if (text.includes('expansion') || text.includes('tank')) {
      data.productType = 'Expansion Tank';
    }
  }

  // Extract manufacturer
  if (text.includes('MAHLE')) {
    data.manufacturer = 'MAHLE';
    if (text.includes('BEHR')) {
      data.manufacturer += ' BEHR';
    }
  }

  // Extract vehicle compatibility
  const vehicleRegex = /BUICK|OPEL|FORD|MERCEDES|BMW|AUDI|VW|VOLKSWAGEN|CHEVROLET|TOYOTA/i;
  const vehicleMatches = text.match(new RegExp(`(${vehicleRegex.source}[^\\n]+)`, 'i'));
  if (vehicleMatches) {
    data.vehicleCompatibility = vehicleMatches[0].trim();
  }

  // Extract expiry date or production code (usually numeric)
  const expiryDateRegex = /[0-9]{8}/;
  const expiryMatch = text.match(expiryDateRegex);
  if (expiryMatch) {
    data.expiryDate = expiryMatch[0];
  }

  // Check for quality grade if not already set from part number
  if (!data.qualityGrade) {
    const lowerText = text.toLowerCase();
    if (lowerText.includes(' p') || lowerText.includes('premium')) {
      data.qualityGrade = 'premium';
    } else if (lowerText.includes(' s') || lowerText.includes('standard')) {
      data.qualityGrade = 'standard';
    }
  }

  // Extract any serial number (typically longer numbers)
  const serialRegex = /[0-9]{10,}/;
  const serialMatch = text.match(serialRegex);
  if (serialMatch && (!expiryMatch || serialMatch[0] !== expiryMatch[0])) {
    data.serialNumber = serialMatch[0];
  }

  return data;
}