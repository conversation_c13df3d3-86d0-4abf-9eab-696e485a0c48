/**
 * Environment configuration and validation
 * Centralizes environment variable access and provides type safety
 */

// Server-side environment variables
export const serverEnv = {
  // Database
  DATABASE_URL: process.env.DATABASE_URL,
  
  // Authentication
  AUTH_SECRET: process.env.AUTH_SECRET,
  AUTH_RESEND_KEY: process.env.AUTH_RESEND_KEY,
  RESEND_FROM_EMAIL: process.env.RESEND_FROM_EMAIL,
  AUTH_GOOGLE_ID: process.env.AUTH_GOOGLE_ID,
  AUTH_GOOGLE_SECRET: process.env.AUTH_GOOGLE_SECRET,
  
  // Azure OpenAI
  AZURE_OPENAI_API_KEY: process.env.AZURE_OPENAI_API_KEY,
  AZURE_OPENAI_ENDPOINT: process.env.AZURE_OPENAI_ENDPOINT,
  AZURE_OPENAI_VISION_MODEL: process.env.AZURE_OPENAI_VISION_MODEL,
  
  // Cloudflare
  CLOUDFLARE_ACCOUNT_ID: process.env.CLOUDFLARE_ACCOUNT_ID,
  CLOUDFLARE_API_TOKEN: process.env.CLOUDFLARE_API_TOKEN,
  CLOUDFLARE_DOMAIN: process.env.CLOUDFLARE_DOMAIN,
  
  // Application
  NODE_ENV: process.env.NODE_ENV,
  VERCEL_ENV: process.env.VERCEL_ENV,
} as const

// Client-side environment variables (must be prefixed with NEXT_PUBLIC_)
export const clientEnv = {
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  NEXT_PUBLIC_USE_LOCAL_PREVIEW: process.env.NEXT_PUBLIC_USE_LOCAL_PREVIEW,
} as const

// Environment validation
export function validateEnvironment() {
  const errors: string[] = []
  
  // Required server environment variables
  const requiredServerVars = [
    'DATABASE_URL',
    'AUTH_SECRET',
  ] as const
  
  requiredServerVars.forEach(varName => {
    if (!serverEnv[varName]) {
      errors.push(`Missing required environment variable: ${varName}`)
    }
  })
  
  // Validate URLs
  if (serverEnv.DATABASE_URL && !serverEnv.DATABASE_URL.startsWith('postgresql://')) {
    errors.push('DATABASE_URL must be a valid PostgreSQL connection string')
  }
  
  if (clientEnv.NEXT_PUBLIC_APP_URL) {
    try {
      new URL(clientEnv.NEXT_PUBLIC_APP_URL)
    } catch {
      errors.push('NEXT_PUBLIC_APP_URL must be a valid URL')
    }
  }
  
  // Validate Azure OpenAI configuration if any part is provided
  const azureVars = [serverEnv.AZURE_OPENAI_API_KEY, serverEnv.AZURE_OPENAI_ENDPOINT]
  const hasAnyAzureVar = azureVars.some(Boolean)
  const hasAllAzureVars = azureVars.every(Boolean)
  
  if (hasAnyAzureVar && !hasAllAzureVars) {
    errors.push('If using Azure OpenAI, both AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT are required')
  }
  
  // Validate Cloudflare configuration if any part is provided
  const cloudflareVars = [
    serverEnv.CLOUDFLARE_ACCOUNT_ID,
    serverEnv.CLOUDFLARE_API_TOKEN,
    serverEnv.CLOUDFLARE_DOMAIN
  ]
  const hasAnyCloudflareVar = cloudflareVars.some(Boolean)
  const hasAllCloudflareVars = cloudflareVars.every(Boolean)
  
  if (hasAnyCloudflareVar && !hasAllCloudflareVars) {
    errors.push('If using Cloudflare, CLOUDFLARE_ACCOUNT_ID, CLOUDFLARE_API_TOKEN, and CLOUDFLARE_DOMAIN are all required')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Utility functions
export function isDevelopment() {
  return serverEnv.NODE_ENV === 'development'
}

export function isProduction() {
  return serverEnv.NODE_ENV === 'production'
}

export function isTest() {
  return serverEnv.NODE_ENV === 'test'
}

export function getBaseUrl() {
  if (clientEnv.NEXT_PUBLIC_APP_URL) {
    return clientEnv.NEXT_PUBLIC_APP_URL
  }
  
  if (isDevelopment()) {
    return 'http://localhost:3000'
  }
  
  // Fallback for production
  return 'https://warrantio.app'
}

// Feature flags based on environment
export const features = {
  enableOCR: Boolean(serverEnv.AZURE_OPENAI_API_KEY && serverEnv.AZURE_OPENAI_ENDPOINT),
  enableCloudflareUpload: Boolean(
    serverEnv.CLOUDFLARE_ACCOUNT_ID && 
    serverEnv.CLOUDFLARE_API_TOKEN && 
    serverEnv.CLOUDFLARE_DOMAIN
  ),
  enableLocalPreview: clientEnv.NEXT_PUBLIC_USE_LOCAL_PREVIEW === 'true' || isDevelopment(),
  enableGoogleAuth: Boolean(serverEnv.AUTH_GOOGLE_ID && serverEnv.AUTH_GOOGLE_SECRET),
  enableEmailAuth: Boolean(serverEnv.AUTH_RESEND_KEY && serverEnv.RESEND_FROM_EMAIL),
} as const
