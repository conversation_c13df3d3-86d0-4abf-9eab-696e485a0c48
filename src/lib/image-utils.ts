"use client";

// File validation constants
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/heic',
  'image/heif'
] as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_IMAGE_DIMENSION = 4096; // 4K max resolution

/**
 * Validates file upload security constraints
 * @param file - The file to validate
 * @returns Object with validation result and error message
 */
export function validateFileUpload(file: File): { isValid: boolean; error?: string } {
  // Check file type
  if (!ALLOWED_FILE_TYPES.includes(file.type as any)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed. Please upload JPEG, PNG, or HEIC images.`
    };
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size ${(file.size / 1024 / 1024).toFixed(1)}MB exceeds the maximum allowed size of ${MAX_FILE_SIZE / 1024 / 1024}MB.`
    };
  }

  // Check if file name is suspicious
  const suspiciousPatterns = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
  if (suspiciousPatterns.some(pattern => file.name.toLowerCase().includes(pattern))) {
    return {
      isValid: false,
      error: 'File name contains suspicious patterns.'
    };
  }

  return { isValid: true };
}

/**
 * Converts HEIC/HEIF images to PNG format using browser Canvas API
 *
 * @param file The file to convert
 * @returns A Promise that resolves to a File object in PNG format
 */
export async function convertHeicToPng(file: File): Promise<File> {
  try {
    // Validate file before processing
    const validation = validateFileUpload(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Check if this is a HEIC file that needs conversion
    const isHeic = file.name.toLowerCase().endsWith('.heic') ||
                  file.name.toLowerCase().endsWith('.heif');

    if (!isHeic) {
      // If not a HEIC file, just return the original file
      return file;
    }

    console.log(`Converting HEIC file to PNG using Canvas method: ${file.name}, size: ${file.size} bytes`);

    // Use canvas approach for all conversions since the library approach is unreliable
    return await canvasConversion(file);
  } catch (error) {
    console.error('HEIC conversion error:', error instanceof Error ? error.message : String(error));

    // If conversion fails, return the original file
    console.log("Returning original file due to conversion failure");
    return file;
  }
}

/**
 * Canvas-based method to convert image files
 *
 * @param file The file to convert
 * @returns A Promise that resolves to a File object in PNG format
 */
async function canvasConversion(file: File): Promise<File> {
  return new Promise((resolve) => {
    try {
      console.log("Attempting canvas conversion");

      // First, create a blob URL from the file
      const url = URL.createObjectURL(file);

      // Create an Image object to load the image
      const img = new Image();

      // Set up the image load handler
      img.onload = () => {
        try {
          console.log(`Image loaded with dimensions: ${img.width}x${img.height}`);

          // Create canvas and set dimensions
          const canvas = document.createElement('canvas');
          canvas.width = img.width || 800; // Fallback width if image dimensions are not available
          canvas.height = img.height || 600; // Fallback height

          // Get context and draw image
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            throw new Error("Failed to get canvas context");
          }

          // Draw the image on the canvas
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Convert to PNG with quality setting
          canvas.toBlob(
            (blob) => {
              // Clean up the URL to prevent memory leaks
              URL.revokeObjectURL(url);

              if (!blob) {
                console.error("Canvas toBlob returned null");
                resolve(file); // Return original file on error
                return;
              }

              // Create a new file with PNG extension and proper MIME type
              const newFileName = file.name.replace(/\.(heic|heif)$/i, '.png');
              const newFile = new File([blob], newFileName, {
                type: 'image/png', // Explicitly set MIME type
                lastModified: file.lastModified
              });

              console.log(`Canvas conversion successful: ${file.name} → ${newFileName}, size: ${blob.size} bytes, type: ${newFile.type}`);
              resolve(newFile);
            },
            'image/png',
            0.85 // Quality setting
          );
        } catch (err) {
          console.error("Error in canvas processing:", err);
          URL.revokeObjectURL(url);
          resolve(file); // Return original file on error
        }
      };

      // Handle image loading errors
      img.onerror = (err) => {
        console.error("Failed to load image for canvas conversion:", err);
        URL.revokeObjectURL(url);

        // For HEIC files, this is expected in many browsers
        console.log("Browser likely doesn't support HEIC format natively - returning original file");
        resolve(file);
      };

      // Start loading the image
      img.src = url;
      console.log("Image loading started with URL:", url);

    } catch (err) {
      console.error("Fatal error in canvas conversion:", err);
      resolve(file); // Return original file on error
    }
  });
}

/**
 * Checks if a file is an image (including HEIC files)
 *
 * @param file The file to check
 * @returns True if the file is an image
 */
export function isImageFile(file: File): boolean {
  // Check by MIME type first
  if (file.type.startsWith('image/')) {
    return true;
  }

  // Check for HEIC files (iPhone photos)
  const filename = file.name.toLowerCase();
  if ((file.type === 'application/octet-stream' || file.type === '') &&
      (filename.endsWith('.heic') || filename.endsWith('.heif'))) {
    return true;
  }

  return false;
}

/**
 * Creates a URL for an image file, converting HEIC to PNG if needed
 *
 * @param file The image file
 * @returns A Promise that resolves to an object with the URL and converted file
 */
export async function createImageUrl(file: File): Promise<{ url: string, file: File }> {
  try {
    // Check if this is a HEIC file that needs conversion
    const isHeic = file.name.toLowerCase().endsWith('.heic') ||
                  file.name.toLowerCase().endsWith('.heif');

    if (isHeic) {
      console.log("HEIC file detected, attempting conversion");

      try {
        // Try to convert HEIC to PNG
        const convertedFile = await convertHeicToPng(file);

        // If the conversion was successful (different filename), use the converted file
        if (convertedFile.name !== file.name) {
          console.log("Using converted file for URL creation");
          const url = URL.createObjectURL(convertedFile);
          return { url, file: convertedFile };
        } else {
          console.log("Conversion didn't change the file (likely failed) - using original");
          // If the conversion returned the original file, use it directly
          const url = URL.createObjectURL(file);
          return { url, file };
        }
      } catch (conversionError) {
        console.error("Conversion error, falling back to original file:", conversionError);
        const url = URL.createObjectURL(file);
        return { url, file };
      }
    } else {
      // Not a HEIC file, use as is
      const url = URL.createObjectURL(file);
      return { url, file };
    }
  } catch (error) {
    console.error('Error creating image URL:', error instanceof Error ? error.message : String(error));

    // Last resort - try to use the original file
    const url = URL.createObjectURL(file);
    return { url, file };
  }
}