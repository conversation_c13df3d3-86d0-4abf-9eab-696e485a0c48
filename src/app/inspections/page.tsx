import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import prisma from "@/lib/prisma";
import { 
  Table, 
  TableHeader, 
  TableBody, 
  TableRow, 
  TableHead, 
  TableCell 
} from "@/components/ui/table";
import { ArrowRightIcon } from "lucide-react";

export const dynamic = "force-dynamic";

export default async function InspectionsPage() {
  const inspections = await prisma.inspection.findMany({
    orderBy: { createdAt: "desc" },
    select: {
      id: true,
      createdAt: true,
      partType: true,
      installationStatus: true,
      decision: true,
      _count: {
        select: { photos: true },
      },
    },
  });

  return (
    <main className="container py-10 space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold">All Inspections</h1>
        <Link href="/inspection" className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
          New Inspection
        </Link>
      </div>
      <Separator />

      {inspections.length === 0 ? (
        <p className="text-muted-foreground text-center py-10">No inspections recorded yet. Start by creating a new one.</p>
      ) : (
        <div className="border rounded-lg overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Part Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Decision</TableHead>
                <TableHead className="text-center">Photos</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {inspections.map((ins: any) => (
                <TableRow key={ins.id} className="hover:bg-muted/40">
                  <TableCell className="font-medium">
                    <Link href={`/inspections/${ins.id}`} className="hover:underline">
                      {ins.id.substring(0, 8)}...
                    </Link>
                  </TableCell>
                  <TableCell>{new Date(ins.createdAt).toLocaleDateString()}</TableCell>
                  <TableCell className="capitalize">{ins.partType}</TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={ins.installationStatus === 'installed'
                        ? 'bg-green-100 text-green-700 border-green-300 dark:bg-green-800/20 dark:text-green-300 dark:border-green-600'
                        : 'bg-sky-100 text-sky-700 border-sky-300 dark:bg-sky-800/20 dark:text-sky-300 dark:border-sky-600'}>
                      {ins.installationStatus === 'installed' ? 'Installed' : 'Not Installed'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={
                        ins.decision === 'warranty'
                          ? 'bg-green-100 text-green-700 border-green-300 dark:bg-green-800/20 dark:text-green-300 dark:border-green-600'
                          : ins.decision === 'goodwill'
                          ? 'bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-800/20 dark:text-amber-300 dark:border-amber-600'
                          : ins.decision === 'deny'
                          ? 'bg-red-100 text-red-700 border-red-300 dark:bg-red-800/30 dark:text-red-300 dark:border-red-600'
                          : 'bg-slate-100 text-slate-700 border-slate-300 dark:bg-slate-700 dark:text-slate-300 dark:border-slate-600'
                      }
                    >
                      {ins.decision ? ins.decision.charAt(0).toUpperCase() + ins.decision.slice(1) : 'Pending'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">{ins._count.photos}</TableCell>
                  <TableCell className="text-right">
                    <Link href={`/inspections/${ins.id}`} className="text-primary hover:underline flex items-center justify-end">
                      View <ArrowRightIcon className="ml-1 h-4 w-4" />
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </main>
  );
} 