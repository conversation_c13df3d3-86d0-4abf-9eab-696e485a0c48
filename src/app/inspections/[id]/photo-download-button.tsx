"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
// @ts-ignore: no types for jszip
import JSZip from "jszip";
import { toast } from "sonner";

interface PhotoDownloadButtonProps {
  photos: { id: string; cdnUrl: string; cdnId: string }[];
}

export default function PhotoDownloadButton({ photos }: PhotoDownloadButtonProps) {
  const [downloading, setDownloading] = useState(false);

  const handleDownload = async () => {
    if (photos.length === 0) return;
    setDownloading(true);
    try {
      const zip = new JSZip();
      const accountHash = process.env.NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_HASH;
      for (const photo of photos) {
        const downloadUrl = photo.cdnUrl;
        const res = await fetch(downloadUrl, { cache: "no-store" });
        if (!res.ok) {
          console.error("Fetch failed", downloadUrl, res.status);
          continue;
        }
        const buf = Buffer.from(await res.arrayBuffer());
        console.log("adding", downloadUrl, buf.length, "bytes");
        zip.file(photo.id + '.jpg', buf);
      }
      const content = await zip.generateAsync({ type: 'blob' });
      const blobUrl = URL.createObjectURL(content);
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = `photos_${Date.now()}.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(blobUrl);
      toast.success('Photos zipped and download started');
    } catch (err) {
      console.error('Error preparing zip:', err);
      toast.error('Failed to download photos');
    } finally {
      setDownloading(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={photos.length === 0 || downloading}
      variant="outline"
      size="sm"
    >
      <Download className="mr-2 h-4 w-4" />
      {downloading ? "Downloading..." : "Download Photos"}
    </Button>
  );
} 