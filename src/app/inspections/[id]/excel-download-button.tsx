"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileSpreadsheet } from "lucide-react";
import { exportInspectionToExcel } from "@/actions/inspections/export-excel";
import { toast } from "sonner";

interface ExcelDownloadButtonProps {
  inspectionId: string;
}

export function ExcelDownloadButton({ inspectionId }: ExcelDownloadButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleExport = async () => {
    try {
      setIsGenerating(true);
      toast.loading("Generating Excel report...");

      const result = await exportInspectionToExcel(inspectionId);
      
      if (!result) {
        toast.error("Failed to generate Excel report");
        return;
      }
      
      // Convert base64 to blob
      const binaryString = atob(result.base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      // Create a download link
      const blob = new Blob([bytes], { 
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success("Excel report generated successfully");
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast.error("Failed to generate Excel report: " + (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button 
      onClick={handleExport} 
      disabled={isGenerating}
      variant="outline"
      size="sm"
    >
      <FileSpreadsheet className="h-4 w-4 mr-2" />
      Download Excel
    </Button>
  );
} 