import prisma from "@/lib/prisma";
import { notFound } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON><PERSON><PERSON>, AlertTriangle, MapPin, Calendar } from "lucide-react";
import Link from "next/link";

import { ExcelDownloadButton } from "./excel-download-button";
// @ts-ignore: module exists but TS can't resolve it
import PhotoDownloadButton from "./photo-download-button";

interface Props {
  params: { id: string };
}

export const dynamic = "force-dynamic";

export default async function InspectionDetailPage({ params }: Props) {
  const { id } = params;

  const inspection = await prisma.inspection.findUnique({
    where: { id: id },
    include: {
      photos: true,
      visits: {
        orderBy: { scheduledDate: "desc" },
      },
    },
  });

  if (!inspection) return notFound();

  // Extract JSON fields for display
  const partDetails = inspection.partDetails as any;
  const vehicleInfo = inspection.vehicleInfo as any;
  const uninstalledInfo = inspection.uninstalledInfo as any;
  const installedChecklist = inspection.installedChecklist as any;

  return (
    <main className="container py-10 space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-3">
          <h1 className="text-2xl font-bold">Inspection Detail</h1>
          <Badge
            variant="outline"
            className={`capitalize ${
              inspection.decision === 'warranty'
                ? 'bg-green-100 text-green-700 border-green-300 dark:bg-green-800/20 dark:text-green-300 dark:border-green-600'
                : inspection.decision === 'goodwill'
                ? 'bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-800/20 dark:text-amber-300 dark:border-amber-600'
                : inspection.decision === 'deny'
                ? 'bg-red-100 text-red-700 border-red-300 dark:bg-red-800/30 dark:text-red-300 dark:border-red-600'
                : 'bg-slate-100 text-slate-700 border-slate-300 dark:bg-slate-700 dark:text-slate-300 dark:border-slate-600'
            }`}>
            {inspection.decision || "Pending"}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <ExcelDownloadButton inspectionId={id} />
          <PhotoDownloadButton photos={inspection.photos.map(p => ({ id: p.id, cdnUrl: p.cdnUrl, cdnId: p.cdnId }))} />
        </div>
      </div>

      <Separator />

      {/* Responsive information grid */}
      <section className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Basic Info */}
        <Card>
          <CardHeader><CardTitle>Basic Info</CardTitle></CardHeader>
          <CardContent>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between"><dt className="text-muted-foreground">Part Type</dt><dd>{inspection.partType}</dd></div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Status</dt>
                <dd>
                  <Badge
                    variant="outline"
                    className={inspection.installationStatus === 'installed'
                      ? 'bg-green-100 text-green-700 border-green-300 dark:bg-green-800/20 dark:text-green-300 dark:border-green-600'
                      : 'bg-sky-100 text-sky-700 border-sky-300 dark:bg-sky-800/20 dark:text-sky-300 dark:border-sky-600'}>
                    {inspection.installationStatus === 'installed' ? 'Installed' : 'Not Installed'}
                  </Badge>
                </dd>
              </div>
              <div className="flex justify-between"><dt className="text-muted-foreground">Created</dt><dd>{new Date(inspection.createdAt).toLocaleString()}</dd></div>
            </dl>
          </CardContent>
        </Card>

        {/* Part Details */}
        <Card>
          <CardHeader><CardTitle>Part Details</CardTitle></CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 text-sm">
              {[
                ['Part Number', partDetails.partNumber],
                ['Product Code', partDetails.productCode],
                ['Serial', partDetails.serialNumber],
                ['Expiry', partDetails.expiryDate],
                ['Manufacturer', partDetails.manufacturer],
                ['Quality', partDetails.qualityGrade],
              ].map(([label, value]) => (
                <div key={label} className="flex justify-between"><dt className="text-muted-foreground">{label}</dt><dd>{value || '—'}</dd></div>
              ))}
            </dl>
          </CardContent>
        </Card>

        {/* Vehicle or Package details based on status */}
        {inspection.installationStatus === 'installed' ? (
          <Card>
            <CardHeader><CardTitle>Vehicle Info</CardTitle></CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 gap-2 text-sm">
                {[
                  ['Make', vehicleInfo.make],
                  ['Model', vehicleInfo.model],
                  ['VIN', vehicleInfo.vinNumber],
                  ['Mileage', vehicleInfo.mileage],
                  ['Install Date', vehicleInfo.installationDate],
                ].map(([label, value]) => (
                  <div key={label} className="flex justify-between"><dt className="text-muted-foreground">{label}</dt><dd>{value || '—'}</dd></div>
                ))}
              </dl>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader><CardTitle>Package Info</CardTitle></CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 gap-2 text-sm">
                {[
                  ['Box Condition', uninstalledInfo.boxCondition],
                  ['Damaged', uninstalledInfo.hasPackageDamage ? 'Yes' : 'No'],
                  ['Stored Properly', uninstalledInfo.storedProperly ? 'Yes' : 'No'],
                  ['Seal Intact', uninstalledInfo.sealIntact ? 'Yes' : 'No'],
                  ['Accessories', uninstalledInfo.accessoriesPresent ? 'Yes' : 'No'],
                ].map(([label, value]) => (
                  <div key={label} className="flex justify-between"><dt className="text-muted-foreground">{label}</dt><dd>{value || '—'}</dd></div>
                ))}
              </dl>
            </CardContent>
          </Card>
        )}
      </section>

      {/* Maintenance Checklist / Reason for Claim */}
      {inspection.installationStatus === 'installed' && (
        <Card>
          <CardHeader><CardTitle>Maintenance Checklist</CardTitle></CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              {[
                ['Flush Performed', installedChecklist.flushPerformed],
                ['Correct Coolant', installedChecklist.correctCoolant],
                ['Thermostat Replaced', installedChecklist.thermostatReplaced],
              ].map(([label, ok]) => (
                <li key={label} className="flex items-center gap-2">
                  {ok ? <CheckCircle className="text-green-500 h-4 w-4" /> : <AlertTriangle className="text-destructive h-4 w-4" />}
                  <span>{label}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {inspection.installationStatus === 'not_installed' && uninstalledInfo.reasonForClaim && (
        <Card>
          <CardHeader><CardTitle>Reason for Claim</CardTitle></CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">{uninstalledInfo.reasonForClaim}</p>
          </CardContent>
        </Card>
      )}

      {/* Photos */}
      {inspection.photos.length > 0 && (
        <Card>
          <CardHeader><CardTitle>Photos ({inspection.photos.length})</CardTitle></CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {inspection.photos.map((p) => (
                <a key={p.id} href={p.cdnUrl} target="_blank" rel="noopener noreferrer" className="block group">
                  <img src={p.cdnUrl} alt={p.caption} className="w-full h-32 object-cover rounded-md border group-hover:opacity-90" />
                </a>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes */}
      {inspection.notes && (
        <Card>
          <CardHeader><CardTitle>Notes</CardTitle></CardHeader>
          <CardContent><p className="text-sm whitespace-pre-wrap">{inspection.notes}</p></CardContent>
        </Card>
      )}

      {/* Visits */}
      {inspection.visits && inspection.visits.length > 0 && (
        <Card>
          <CardHeader><CardTitle>Customer Visits ({inspection.visits.length})</CardTitle></CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {inspection.visits.map((v: any) => (
                <div key={v.id} className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 p-3 rounded-md border hover:bg-muted/40">
                  <div className="flex-1">
                    <h4 className="font-medium">{v.customerName}</h4>
                    <p className="text-xs text-muted-foreground">{v.companyName}</p>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-3 w-3 text-muted-foreground" /> {v.location}
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-3 w-3 text-muted-foreground" /> {new Date(v.scheduledDate).toLocaleDateString()}
                  </div>
                  <Badge
                    variant="outline"
                    className={`capitalize ${v.status === 'completed' ? 'bg-green-100 text-green-700 border-green-300 dark:bg-green-800/20 dark:text-green-300 dark:border-green-600' : v.status === 'scheduled' ? 'bg-sky-100 text-sky-700 border-sky-300 dark:bg-sky-800/20 dark:text-sky-300 dark:border-sky-600' : v.status === 'in_progress' ? 'bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-800/20 dark:text-amber-300 dark:border-amber-600' : 'bg-red-100 text-red-700 border-red-300 dark:bg-red-800/30 dark:text-red-300 dark:border-red-600'}`}> {v.status.replace('_',' ')}
                  </Badge>
                  <Link href={`/visits/${v.id}`} className="text-primary hover:underline text-sm">View</Link>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Schedule visit */}
      <div className="text-right">
        <Link href={`/visits/new?inspectionId=${inspection.id}`} className="inline-flex items-center justify-center rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-1.5">Schedule Visit</Link>
      </div>
    </main>
  );
} 