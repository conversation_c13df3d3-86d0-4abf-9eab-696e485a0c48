import { Suspense } from "react";
import WarrantyInspectionForm from "@/components/warranty/inspection-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function InspectionPage() {
  return (
    <div className="container mx-auto flex items-center justify-center">
    
      
      
          <Card className="max-w-5xl mx-auto">
            <CardHeader className="hidden sm:block">
              <CardTitle>New Inspection</CardTitle>
              <CardDescription className="hidden sm:block">
                Complete the step-by-step process to document warranty inspection details and generate Excel report
              </CardDescription>
            </CardHeader>
            <CardContent className="">
              <Suspense fallback={<div className="py-10 text-center text-muted-foreground">Loading inspection form...</div>}>
                <WarrantyInspectionForm />
              </Suspense>
            </CardContent>
          </Card>
        
      
    </div>
  );
} 