import { Suspense } from "react";
import WarrantyInspectionForm from "@/components/warranty/inspection-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InspectionErrorBoundary } from "@/components/ui/error-boundary";
import { FormLoadingSpinner } from "@/components/ui/loading-spinner";

export default function InspectionPage() {
  return (
    <div className="container mx-auto flex items-center justify-center">
      <Card className="max-w-5xl mx-auto">
        <CardHeader className="hidden sm:block">
          <CardTitle>New Inspection</CardTitle>
          <CardDescription className="hidden sm:block">
            Complete the step-by-step process to document warranty inspection details and generate Excel report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <InspectionErrorBoundary>
            <Suspense fallback={<FormLoadingSpinner text="Loading inspection form..." />}>
              <WarrantyInspectionForm />
            </Suspense>
          </InspectionErrorBoundary>
        </CardContent>
      </Card>
    </div>
  );
}