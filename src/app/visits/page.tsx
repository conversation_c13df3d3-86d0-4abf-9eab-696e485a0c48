import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import prisma from "@/lib/prisma";
import { 
  Table, 
  TableHeader, 
  TableBody, 
  TableRow, 
  TableHead, 
  TableCell 
} from "@/components/ui/table";
import { ArrowRightIcon, Calendar, MapPin } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";

export const dynamic = "force-dynamic";

export default async function VisitsPage() {
  const visits = await prisma.visit.findMany({
    orderBy: { scheduledDate: "desc" },
    include: {
      inspection: true
    }
  });

  function getStatusBadgeClass(status: string) {
    switch (status) {
      case 'scheduled':
        return 'bg-sky-100 text-sky-700 border-sky-300 dark:bg-sky-800/20 dark:text-sky-300 dark:border-sky-600';
      case 'in_progress':
        return 'bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-800/20 dark:text-amber-300 dark:border-amber-600';
      case 'completed':
        return 'bg-green-100 text-green-700 border-green-300 dark:bg-green-800/20 dark:text-green-300 dark:border-green-600';
      case 'canceled':
        return 'bg-red-100 text-red-700 border-red-300 dark:bg-red-800/30 dark:text-red-300 dark:border-red-600';
      default:
        return 'bg-slate-100 text-slate-700 border-slate-300 dark:bg-slate-700 dark:text-slate-300 dark:border-slate-600';
    }
  }

  return (
    <main className="container py-10 space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold">Customer Visits</h1>
        <Link href="/visits/new" className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
          Schedule New Visit
        </Link>
      </div>
      <Separator />

      {visits.length === 0 ? (
        <p className="text-muted-foreground text-center py-10">No visits scheduled yet. Start by scheduling a new customer visit.</p>
      ) : (
        <div className="border rounded-lg overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-center">Progress</TableHead>
                <TableHead className="hidden md:table-cell">Linked Inspection</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {visits.map((visit) => (
                <TableRow key={visit.id} className="hover:bg-muted/40">
                  <TableCell className="font-medium">
                    {visit.customerName}
                    {visit.companyName && (
                      <div className="text-xs text-muted-foreground">{visit.companyName}</div>
                    )}
                  </TableCell>
                  <TableCell className="whitespace-nowrap">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      {visit.location}
                    </div>
                  </TableCell>
                  <TableCell className="whitespace-nowrap">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      {format(new Date(visit.scheduledDate), 'MMM d, yyyy')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={`capitalize ${getStatusBadgeClass(visit.status)}`}>
                      {visit.status.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="inline-flex gap-1 font-medium">
                      <span>{visit.checksCompleted}</span>
                      <span className="text-muted-foreground">/</span>
                      <span>{visit.totalChecks}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {visit.inspection ? (
                      <Link href={`/inspections/${visit.inspection.id}`} className="text-primary hover:underline">
                        {visit.inspection.id.substring(0, 8)}...
                      </Link>
                    ) : (
                      <span className="text-muted-foreground">—</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Link href={`/visits/${visit.id}`} className="text-primary hover:underline flex items-center justify-end">
                      View <ArrowRightIcon className="ml-1 h-4 w-4" />
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </main>
  );
} 