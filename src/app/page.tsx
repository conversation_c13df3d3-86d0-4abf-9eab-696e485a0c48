import prisma from "@/lib/prisma";
import { HeroSection } from "@/components/page/HeroSection";
import { StatsDisplay } from "@/components/page/StatsDisplay";
import { RecentInspectionsCard } from "@/components/page/RecentInspectionsCard";
import { SopReferencesCard } from "@/components/page/SopReferencesCard";
import { ImportantNoticeCard } from "@/components/page/ImportantNoticeCard";

// @ts-ignore - prisma types generated by migration
const client = prisma as any;

/**
 * Home page component - the main landing page for NexusKit
 * Showcases key features and provides navigation to documentation
 */
export default async function Home() {
  // Fetch real inspection stats
  const total = await client.inspection.count();
  const warrantyCount = await client.inspection.count({ where: { decision: "warranty" } });
  const goodwillCount = await client.inspection.count({ where: { decision: "goodwill" } });
  const denyCount = await client.inspection.count({ where: { decision: "deny" } });
  const stats = [
    { label: "Total Inspections", value: total },
    { label: "Warranty", value: warrantyCount },
    { label: "Goodwill", value: goodwillCount },
    { label: "Denied", value: denyCount },
  ];
  // Fetch recent inspections
  const recentsRaw = await client.inspection.findMany({
    orderBy: { createdAt: "desc" },
    take: 3,
  });
  const recentInspections = recentsRaw.map((ins: any) => ({
    id: ins.id,
    date: ins.createdAt.toISOString(),
    part: ins.partType,
    decision: ins.decision.charAt(0).toUpperCase() + ins.decision.slice(1),
    status: ins.decision === "deny" ? "Pending" : "Approved",
  }));

  return (
    <main className="container mx-auto flex flex-col items-center min-h-[70vh] px-4 py-12 md:py-16">
      <HeroSection />
      <StatsDisplay stats={stats} />

      <section className="w-full max-w-5xl py-12 my-10">
        <div className="container grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          <RecentInspectionsCard inspections={recentInspections} />
          <SopReferencesCard />
          <ImportantNoticeCard />
        </div>
      </section>
    </main>
  );
}
