import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { Toaster } from "sonner";
import { Header } from "@/components/header";

/**
 * Geist Sans font configuration
 * Modern, clean sans-serif font for UI elements
 */
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

/**
 * Geist Mono font configuration
 * Monospace font for code blocks and technical content
 */
const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

/**
 * Metadata for the application
 * Customize these values for your project
 */
export const metadata: Metadata = {
  title: "Warranty Inspection App",
  description: "A comprehensive solution for inspecting and documenting warranty claims for thermal management parts in engine cooling and AC systems.",
  keywords: ["warranty", "inspection", "thermal management", "automotive", "engine cooling", "AC systems"],
  authors: [
    {
      name: "Warranty Inspection Team",
    },
  ],
  creator: "Warranty Inspection Team",
};

/**
 * Root layout component that wraps all pages
 * Provides theme support, font loading, and toast notifications
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen`}
      >
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
          <div className="relative flex min-h-screen flex-col items-center justify-center bg-background">
            <Header />
            <div className="w-full max-w-7xl flex-1 flex flex-col items-center justify-center py-8">
              {children}
            </div>
            <Toaster position="top-right" richColors />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
