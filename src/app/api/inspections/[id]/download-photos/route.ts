import prisma from "@/lib/prisma";
import { PassThrough } from "stream";
// @ts-ignore: no types for archiver
import archiver from "archiver";

export const runtime = 'nodejs';

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const inspection = await prisma.inspection.findUnique({
    where: { id },
    include: { photos: true },
  });
  if (!inspection) {
    return new Response(JSON.stringify({ error: 'Not found' }), {
      status: 404,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const photos = inspection.photos;
  const accountHash = process.env.CLOUDFLARE_ACCOUNT_HASH;

  // Create a passthrough stream for the ZIP
  const zipStream = new PassThrough();
  const archive = archiver('zip', { zlib: { level: 9 } });
  archive.on('error', (err: any) => {
    console.error('Archiver error:', err);
    zipStream.destroy(err as any);
  });
  archive.pipe(zipStream);

  // Append each photo to the archive
  for (const photo of photos) {
    let downloadUrl = accountHash && photo.cdnId
      ? `https://imagedelivery.net/${accountHash}/${photo.cdnId}/original`
      : photo.cdnUrl;
    let res = await fetch(downloadUrl);
    if (!res.ok) {
      // fallback to cdnUrl variant
      downloadUrl = photo.cdnUrl;
      res = await fetch(downloadUrl);
    }
    if (!res.ok || !res.body) {
      console.error('Failed to fetch photo:', downloadUrl, res.status);
      continue;
    }
    const extension = downloadUrl.split('.').pop()?.split(/[?#]/)[0] || 'jpg';
    const filename = `${photo.id}.${extension}`;
    // Stream directly into archive
    archive.append(res.body as any, { name: filename });
  }

  await archive.finalize();

  // @ts-ignore: PassThrough stream is supported as response body
  return new Response(zipStream, {
    headers: {
      'Content-Type': 'application/zip',
      'Content-Disposition': `attachment; filename="inspection_${id}_photos.zip"`,
    },
  });
} 