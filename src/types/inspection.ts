export interface InspectionFormDataType {
  [key: string]: any;
  language: string;
  partType: string;
  installationStatus: string;
  partDetails: {
    partNumber: string;
    productCode: string;
    serialNumber: string;
    expiryDate: string;
    manufacturer: string;
    qualityGrade: string;
    vehicleCompatibility: string;
  };
  vehicleInfo: {
    make: string;
    model: string;
    vinNumber: string;
    mileage: string;
    installationDate: string;
  };
  uninstalledInfo: {
    boxCondition: string;
    hasPackageDamage: boolean;
    storedProperly: boolean;
    sealIntact: boolean;
    accessoriesPresent: boolean;
    reasonForClaim: string;
  };
  installedChecklist: {
    flushPerformed: boolean;
    correctCoolant: boolean;
    thermostatReplaced: boolean;
  };
  photos: {
    id: string;
    url: string;
    caption: string;
    group?: string;
    ocrConfidence?: number;
    originalFileName?: string;
    convertedFileName?: string;
    imageId?: string;
  }[];
  decision: string;
  notes: string;
} 